"""Memory management module for cryptocurrency backtesting system.

This module provides advanced memory management capabilities optimized for
large-scale backtesting operations with 96GB DDR5 RAM, including memory
monitoring, garbage collection optimization, and memory-efficient data structures.

Classes:
    MemoryManager: Main memory management coordinator
    MemoryMonitor: Real-time memory usage monitoring
    DataChunker: Efficient data chunking for large datasets
    CacheManager: Intelligent caching with memory limits
    GarbageCollector: Optimized garbage collection strategies
    MemoryPool: Pre-allocated memory pools for performance

Functions:
    monitor_memory: Monitor system memory usage
    optimize_gc: Optimize garbage collection settings
    chunk_data: Split large datasets into memory-efficient chunks
    clear_cache: Clear system caches

Example:
    >>> from src.memory import MemoryManager, DataChunker
    >>> 
    >>> # Initialize memory manager for 96GB system
    >>> memory_mgr = MemoryManager(total_memory_gb=96, max_usage=0.8)
    >>> memory_mgr.start_monitoring()
    >>> 
    >>> # Chunk large dataset for processing
    >>> chunker = DataChunker(chunk_size_mb=500)
    >>> chunks = chunker.chunk_dataframe(large_dataframe)
    >>> 
    >>> # Process chunks with memory monitoring
    >>> for chunk in chunks:
    >>>     if memory_mgr.check_memory_available(required_gb=2):
    >>>         result = process_chunk(chunk)
    >>>     else:
    >>>         memory_mgr.force_cleanup()
"""

# Import memory management components
try:
    from .manager import MemoryManager
    from .monitor import MemoryMonitor
    from .chunker import DataChunker
    from .cache_manager import CacheManager
    from .gc_optimizer import GarbageCollector
    from .memory_pool import MemoryPool
    from .profiler import MemoryProfiler
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Memory configuration optimized for 96GB DDR5 system
MEMORY_CONFIG_96GB = {
    'total_memory_gb': 96,
    'max_usage_ratio': 0.8,         # Use up to 80% (76.8GB)
    'warning_threshold': 0.7,       # Warning at 70% (67.2GB)
    'critical_threshold': 0.85,     # Critical at 85% (81.6GB)
    'cleanup_threshold': 0.75,      # Cleanup at 75% (72GB)
    'chunk_size_mb': 512,           # 512MB chunks
    'cache_size_gb': 8,             # 8GB cache
    'gc_threshold': 0.8,            # GC at 80% usage
    'monitoring_interval': 5        # 5 second monitoring
}

# Conservative memory configuration
MEMORY_CONFIG_CONSERVATIVE = {
    'total_memory_gb': 32,
    'max_usage_ratio': 0.7,         # Use up to 70%
    'warning_threshold': 0.6,       # Warning at 60%
    'critical_threshold': 0.8,      # Critical at 80%
    'cleanup_threshold': 0.65,      # Cleanup at 65%
    'chunk_size_mb': 256,           # 256MB chunks
    'cache_size_gb': 4,             # 4GB cache
    'gc_threshold': 0.7,            # GC at 70% usage
    'monitoring_interval': 10       # 10 second monitoring
}

# Data chunking strategies
CHUNKING_STRATEGIES = {
    'time_based': 'Split data by time periods',
    'size_based': 'Split data by memory size',
    'row_based': 'Split data by number of rows',
    'adaptive': 'Adaptive chunking based on available memory',
    'balanced': 'Balanced chunking for parallel processing'
}

# Cache policies
CACHE_POLICIES = {
    'lru': 'Least Recently Used',
    'lfu': 'Least Frequently Used',
    'fifo': 'First In, First Out',
    'adaptive': 'Adaptive replacement cache',
    'time_based': 'Time-based expiration'
}

# Garbage collection strategies
GC_STRATEGIES = {
    'aggressive': {
        'threshold0': 500,
        'threshold1': 8,
        'threshold2': 8,
        'frequency': 'high'
    },
    'balanced': {
        'threshold0': 700,
        'threshold1': 10,
        'threshold2': 10,
        'frequency': 'medium'
    },
    'conservative': {
        'threshold0': 1000,
        'threshold1': 15,
        'threshold2': 15,
        'frequency': 'low'
    }
}

# Memory monitoring metrics
MONITORING_METRICS = [
    'total_memory',
    'available_memory',
    'used_memory',
    'memory_percent',
    'swap_memory',
    'cached_memory',
    'buffer_memory',
    'gc_collections',
    'gc_time',
    'allocation_rate',
    'deallocation_rate'
]

# Memory optimization settings
OPTIMIZATION_SETTINGS = {
    'enable_memory_mapping': True,   # Use memory mapping for large files
    'enable_compression': True,      # Compress data in memory
    'enable_lazy_loading': True,     # Load data on demand
    'enable_object_pooling': True,   # Reuse objects
    'enable_copy_on_write': True,    # Copy-on-write optimization
    'prefetch_size': 100,            # Prefetch buffer size
    'alignment': 64                  # Memory alignment (cache line)
}

# Alert thresholds
ALERT_THRESHOLDS = {
    'memory_leak_mb_per_hour': 100,  # 100MB/hour indicates potential leak
    'gc_frequency_per_minute': 10,   # More than 10 GC/minute is concerning
    'allocation_rate_mb_per_sec': 50, # High allocation rate threshold
    'fragmentation_ratio': 0.3,      # 30% fragmentation threshold
    'swap_usage_mb': 1024           # 1GB swap usage threshold
}

def monitor_memory(interval=5, callback=None):
    """Monitor system memory usage.
    
    Args:
        interval: Monitoring interval in seconds
        callback: Optional callback function for alerts
        
    Returns:
        MemoryMonitor: Memory monitor instance
    """
    # This would be implemented when the actual memory classes are created
    raise NotImplementedError("monitor_memory will be implemented with memory classes")

def optimize_gc(strategy='balanced'):
    """Optimize garbage collection settings.
    
    Args:
        strategy: GC strategy ('aggressive', 'balanced', 'conservative')
    """
    # This would be implemented when the actual GC classes are created
    raise NotImplementedError("optimize_gc will be implemented with GC classes")

def chunk_data(data, strategy='adaptive', chunk_size=None):
    """Split large datasets into memory-efficient chunks.
    
    Args:
        data: Data to chunk (DataFrame, array, etc.)
        strategy: Chunking strategy
        chunk_size: Optional chunk size override
        
    Returns:
        Iterator: Chunk iterator
    """
    # This would be implemented when the actual chunker classes are created
    raise NotImplementedError("chunk_data will be implemented with chunker classes")

def clear_cache(cache_type='all'):
    """Clear system caches.
    
    Args:
        cache_type: Type of cache to clear ('all', 'data', 'results', 'temp')
    """
    # This would be implemented when the actual cache classes are created
    raise NotImplementedError("clear_cache will be implemented with cache classes")

# Export public API
__all__ = [
    "MemoryManager",
    "MemoryMonitor",
    "DataChunker",
    "CacheManager",
    "GarbageCollector",
    "MemoryPool",
    "MemoryProfiler",
    "monitor_memory",
    "optimize_gc",
    "chunk_data",
    "clear_cache",
    "MEMORY_CONFIG_96GB",
    "MEMORY_CONFIG_CONSERVATIVE",
    "CHUNKING_STRATEGIES",
    "CACHE_POLICIES",
    "GC_STRATEGIES",
    "MONITORING_METRICS",
    "OPTIMIZATION_SETTINGS",
    "ALERT_THRESHOLDS",
]