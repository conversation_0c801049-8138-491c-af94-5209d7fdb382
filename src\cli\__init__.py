"""Command-line interface module for cryptocurrency backtesting system.

This module provides a comprehensive CLI for managing backtesting operations,
strategy optimization, system monitoring, and configuration management.

Classes:
    CLI: Main command-line interface
    BacktestCommand: Backtest execution commands
    OptimizeCommand: Parameter optimization commands
    DataCommand: Data management commands
    ConfigCommand: Configuration management commands
    MonitorCommand: System monitoring commands
    ReportCommand: Report generation commands

Functions:
    main: Main CLI entry point
    run_backtest_cli: Run backtest from command line
    run_optimization_cli: Run optimization from command line
    setup_cli: Setup CLI configuration

Example:
    Command line usage:
    
    # Run a simple backtest
    $ crypto-backtester backtest --strategy ma_crossover --symbol BTC/USDT --timeframe 1h
    
    # Run parameter optimization
    $ crypto-backtester optimize --strategy ma_crossover --symbol BTC/USDT \
      --param-range fast_period:10:50:5 --param-range slow_period:20:200:10 \
      --workers 16 --objective sharpe_ratio
    
    # Fetch and prepare data
    $ crypto-backtester data fetch --exchange binance --symbol BTC/USDT \
      --timeframe 1h --start 2023-01-01 --end 2023-12-31
    
    # Start system monitoring
    $ crypto-backtester monitor --dashboard --port 8080
    
    # Generate performance report
    $ crypto-backtester report --input results.json --output report.html
"""

# Import CLI components
try:
    from .main import CLI
    from .commands.backtest import BacktestCommand
    from .commands.optimize import OptimizeCommand
    from .commands.data import DataCommand
    from .commands.config import ConfigCommand
    from .commands.monitor import MonitorCommand
    from .commands.report import ReportCommand
    from .utils import CLIUtils
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# CLI configuration
CLI_CONFIG = {
    'app_name': 'crypto-backtester',
    'version': '0.1.0',
    'description': 'High-performance cryptocurrency backtesting system',
    'epilog': 'For more information, visit: https://github.com/your-username/crypto-backtester',
    'default_config': 'config/development.yaml',
    'log_level': 'INFO',
    'progress_bar': True,
    'colored_output': True
}

# Command definitions
COMMANDS = {
    'backtest': {
        'description': 'Run backtesting operations',
        'class': 'BacktestCommand',
        'aliases': ['bt', 'test']
    },
    'optimize': {
        'description': 'Run parameter optimization',
        'class': 'OptimizeCommand',
        'aliases': ['opt', 'search']
    },
    'data': {
        'description': 'Data management operations',
        'class': 'DataCommand',
        'aliases': ['d']
    },
    'config': {
        'description': 'Configuration management',
        'class': 'ConfigCommand',
        'aliases': ['cfg', 'conf']
    },
    'monitor': {
        'description': 'System monitoring and dashboard',
        'class': 'MonitorCommand',
        'aliases': ['mon', 'watch']
    },
    'report': {
        'description': 'Generate performance reports',
        'class': 'ReportCommand',
        'aliases': ['rep']
    }
}

# Global CLI options
GLOBAL_OPTIONS = {
    '--config': {
        'type': str,
        'help': 'Configuration file path',
        'default': 'config/development.yaml'
    },
    '--log-level': {
        'type': str,
        'choices': ['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        'help': 'Logging level',
        'default': 'INFO'
    },
    '--log-file': {
        'type': str,
        'help': 'Log file path',
        'default': None
    },
    '--workers': {
        'type': int,
        'help': 'Number of parallel workers',
        'default': 16
    },
    '--memory-limit': {
        'type': int,
        'help': 'Memory limit in GB',
        'default': 76
    },
    '--no-color': {
        'action': 'store_true',
        'help': 'Disable colored output',
        'default': False
    },
    '--quiet': {
        'action': 'store_true',
        'help': 'Suppress output',
        'default': False
    },
    '--verbose': {
        'action': 'store_true',
        'help': 'Verbose output',
        'default': False
    }
}

# Backtest command options
BACKTEST_OPTIONS = {
    '--strategy': {
        'type': str,
        'required': True,
        'help': 'Strategy name or class'
    },
    '--symbol': {
        'type': str,
        'required': True,
        'help': 'Trading symbol (e.g., BTC/USDT)'
    },
    '--timeframe': {
        'type': str,
        'help': 'Timeframe (e.g., 1h, 4h, 1d)',
        'default': '1h'
    },
    '--start': {
        'type': str,
        'help': 'Start date (YYYY-MM-DD)',
        'required': True
    },
    '--end': {
        'type': str,
        'help': 'End date (YYYY-MM-DD)',
        'required': True
    },
    '--exchange': {
        'type': str,
        'help': 'Exchange name',
        'default': 'binance'
    },
    '--initial-cash': {
        'type': float,
        'help': 'Initial cash amount',
        'default': 100000
    },
    '--commission': {
        'type': float,
        'help': 'Commission rate',
        'default': 0.001
    },
    '--output': {
        'type': str,
        'help': 'Output file path',
        'default': None
    }
}

# Optimization command options
OPTIMIZATION_OPTIONS = {
    '--param-range': {
        'action': 'append',
        'help': 'Parameter range (name:min:max:step)',
        'required': True
    },
    '--objective': {
        'type': str,
        'help': 'Optimization objective',
        'choices': ['sharpe_ratio', 'total_return', 'calmar_ratio'],
        'default': 'sharpe_ratio'
    },
    '--algorithm': {
        'type': str,
        'help': 'Optimization algorithm',
        'choices': ['grid_search', 'bayesian', 'genetic'],
        'default': 'grid_search'
    },
    '--max-iterations': {
        'type': int,
        'help': 'Maximum optimization iterations',
        'default': 1000
    },
    '--timeout': {
        'type': int,
        'help': 'Optimization timeout in seconds',
        'default': 3600
    }
}

# Data command options
DATA_OPTIONS = {
    'fetch': {
        '--exchange': {'type': str, 'required': True},
        '--symbol': {'type': str, 'required': True},
        '--timeframe': {'type': str, 'default': '1h'},
        '--start': {'type': str, 'required': True},
        '--end': {'type': str, 'required': True},
        '--output': {'type': str, 'default': None}
    },
    'validate': {
        '--input': {'type': str, 'required': True},
        '--rules': {'type': str, 'default': None}
    },
    'clean': {
        '--input': {'type': str, 'required': True},
        '--output': {'type': str, 'default': None}
    }
}

# Output formats
OUTPUT_FORMATS = {
    'json': 'JSON format',
    'csv': 'CSV format',
    'parquet': 'Parquet format',
    'html': 'HTML report',
    'pdf': 'PDF report'
}

# Exit codes
EXIT_CODES = {
    'SUCCESS': 0,
    'GENERAL_ERROR': 1,
    'INVALID_ARGUMENTS': 2,
    'CONFIG_ERROR': 3,
    'DATA_ERROR': 4,
    'STRATEGY_ERROR': 5,
    'OPTIMIZATION_ERROR': 6,
    'SYSTEM_ERROR': 7
}

def main():
    """Main CLI entry point.
    
    This function is called when the CLI is executed from the command line.
    It parses arguments and dispatches to the appropriate command handler.
    """
    # This would be implemented when the actual CLI classes are created
    raise NotImplementedError("main will be implemented with CLI classes")

def run_backtest_cli(args):
    """Run backtest from command line arguments.
    
    Args:
        args: Parsed command line arguments
        
    Returns:
        int: Exit code
    """
    # This would be implemented when the actual CLI classes are created
    raise NotImplementedError("run_backtest_cli will be implemented with CLI classes")

def run_optimization_cli(args):
    """Run optimization from command line arguments.
    
    Args:
        args: Parsed command line arguments
        
    Returns:
        int: Exit code
    """
    # This would be implemented when the actual CLI classes are created
    raise NotImplementedError("run_optimization_cli will be implemented with CLI classes")

def setup_cli():
    """Setup CLI configuration and logging.
    
    Returns:
        argparse.ArgumentParser: Configured argument parser
    """
    # This would be implemented when the actual CLI classes are created
    raise NotImplementedError("setup_cli will be implemented with CLI classes")

# Export public API
__all__ = [
    "CLI",
    "BacktestCommand",
    "OptimizeCommand",
    "DataCommand",
    "ConfigCommand",
    "MonitorCommand",
    "ReportCommand",
    "CLIUtils",
    "main",
    "run_backtest_cli",
    "run_optimization_cli",
    "setup_cli",
    "CLI_CONFIG",
    "COMMANDS",
    "GLOBAL_OPTIONS",
    "BACKTEST_OPTIONS",
    "OPTIMIZATION_OPTIONS",
    "DATA_OPTIONS",
    "OUTPUT_FORMATS",
    "EXIT_CODES",
]