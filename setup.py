#!/usr/bin/env python3
"""
Setup script for the Cryptocurrency Backtesting System.
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.11+
if sys.version_info < (3, 11):
    raise RuntimeError("This package requires Python 3.11 or later")

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# Development requirements
dev_requirements = [
    "pytest>=7.3.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "black>=23.3.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.3.0",
    "pre-commit>=3.3.0",
]

# Documentation requirements
docs_requirements = [
    "sphinx>=6.2.0",
    "sphinx-rtd-theme>=1.2.0",
    "sphinx-autodoc-typehints>=1.23.0",
]

# Optional ML requirements
ml_requirements = [
    "scikit-learn>=1.2.0",
    "xgboost>=1.7.0",
    "lightgbm>=3.3.0",
    "tensorflow>=2.12.0",
    "torch>=2.0.0",
]

# Web interface requirements
web_requirements = [
    "fastapi>=0.95.0",
    "uvicorn>=0.22.0",
    "streamlit>=1.22.0",
    "jinja2>=3.1.0",
]

# Jupyter requirements
jupyter_requirements = [
    "jupyter>=1.0.0",
    "ipykernel>=6.23.0",
    "ipywidgets>=8.0.0",
    "notebook>=6.5.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=2.0.0",
    "bump2version>=1.0.1",
    "twine>=4.0.2",
]

# Performance dependencies (optional)
performance_requirements = [
    "numba>=0.58.1",
    "dask[complete]>=2023.12.1",
    "ray[default]>=2.8.1",
    "tables>=3.9.2",
    "redis>=5.0.1",
]

# Machine learning dependencies (optional)
ml_requirements = [
    "scikit-learn>=1.3.2",
    "xgboost>=2.0.2",
    "lightgbm>=4.1.0",
    "optuna>=3.5.0",
    "scikit-optimize>=0.9.0",
]

# GPU dependencies (optional)
gpu_requirements = [
    # "cupy-cuda12x>=12.3.0",  # Uncomment if CUDA is available
    # "cudf>=23.12.0",  # Uncomment for GPU DataFrames
]

setup(
    name="crypto-backtester",
    version=get_version(),
    author="Crypto Backtesting Team",
    author_email="<EMAIL>",
    description="High-performance cryptocurrency backtesting system with parallel optimization",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/crypto-backtester",
    project_urls={
        "Bug Tracker": "https://github.com/your-username/crypto-backtester/issues",
        "Documentation": "https://crypto-backtester.readthedocs.io/",
        "Source Code": "https://github.com/your-username/crypto-backtester",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Information Analysis",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Environment :: Console",
        "Natural Language :: English",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": dev_requirements,
        "performance": performance_requirements,
        "ml": ml_requirements,
        "gpu": gpu_requirements,
        "all": dev_requirements + performance_requirements + ml_requirements,
    },
    entry_points={
        "console_scripts": [
            "crypto-backtester=src.main:main",
            "backtest-optimize=src.cli.optimize:main",
            "backtest-data=src.cli.data:main",
            "backtest-strategy=src.cli.strategy:main",
            "backtest-monitor=src.cli.monitor:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": [
            "config/*.yaml",
            "config/*.yml",
            "templates/*.yaml",
            "templates/*.yml",
        ],
    },
    zip_safe=False,
    keywords=[
        "cryptocurrency",
        "backtesting",
        "trading",
        "strategy",
        "optimization",
        "parallel",
        "performance",
        "backtrader",
        "fintech",
        "algorithmic-trading",
    ],
    platforms=["any"],
    license="MIT",
    test_suite="tests",
    tests_require=[
        "pytest>=7.4.3",
        "pytest-cov>=4.1.0",
        "pytest-mock>=3.12.0",
    ],
    cmdclass={},
    options={
        "build_scripts": {
            "executable": "/usr/bin/env python3",
        },
    },
)

# Post-installation message
print("""
🚀 Crypto Backtesting System Installation Complete!

Next Steps:
1. Configure your system: cp config/development.yaml.example config/development.yaml
2. Edit configuration to match your hardware specifications
3. Run initial data fetch: crypto-backtester data fetch --symbol BTC/USDT
4. Start optimization: crypto-backtester optimize --strategy ma_crossover

For detailed documentation, see: README.md and docs/

Hardware Optimization Tips:
- Adjust max_workers in config based on your CPU cores
- Set memory limits based on available RAM
- Use SSD storage for better I/O performance

Support:
- Documentation: README.md, PLANNING.md, TASK.md
- Style Guide: STYLE_GUIDE.md
- AI Guidelines: AI_BEHAVIOR_RULES.md

Happy backtesting! 📈
""")