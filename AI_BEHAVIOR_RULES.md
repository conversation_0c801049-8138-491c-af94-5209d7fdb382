# AI Behavior Rules for Crypto Backtesting System

## AI Assistant Guidelines and Development Principles

### Core Development Philosophy

#### Performance-First Approach
- **Hardware Utilization**: Always consider the available hardware (Ryzen 9 7950X, 96GB RAM) when suggesting solutions
- **Parallel Processing**: Prioritize multi-threaded and multi-process solutions where applicable
- **Memory Efficiency**: Leverage the 96GB RAM effectively while preventing memory leaks
- **CPU Optimization**: Utilize all 32 threads for CPU-intensive operations

#### Quality Assurance Priority
- **Backtesting Integrity**: Never compromise backtesting accuracy for performance gains
- **Deterministic Results**: Ensure reproducible results across multiple runs
- **Data Validation**: Always validate input data before processing
- **Error Recovery**: Implement robust error handling and recovery mechanisms

### Code Generation Rules

#### Mandatory Requirements
1. **Type Hints**: All generated functions must include comprehensive type hints
2. **Docstrings**: Use Google-style docstrings for all functions and classes
3. **Error Handling**: Include appropriate exception handling with specific exception types
4. **Logging**: Add appropriate logging statements for debugging and monitoring
5. **Performance Monitoring**: Include memory and CPU usage monitoring where relevant

#### Code Structure Standards
```python
# Template for AI-generated functions
from typing import Dict, List, Optional, Union, Any
import logging
import time
from pathlib import Path

logger = logging.getLogger(__name__)

def example_function(
    required_param: str,
    optional_param: Optional[int] = None,
    config: Dict[str, Any] = None
) -> Union[Dict[str, Any], None]:
    """Brief description of function purpose.
    
    Detailed description explaining the function's behavior,
    performance characteristics, and any important considerations.
    
    Args:
        required_param: Description of required parameter
        optional_param: Description of optional parameter
        config: Configuration dictionary with specific keys
    
    Returns:
        Description of return value and its structure
    
    Raises:
        SpecificError: When specific condition occurs
        ValueError: When input validation fails
    
    Example:
        >>> result = example_function("test", optional_param=42)
        >>> print(result['status'])
    
    Note:
        Any performance considerations or memory usage notes
    """
    start_time = time.time()
    logger.debug(f"Starting {example_function.__name__} with params: {required_param}")
    
    try:
        # Input validation
        if not required_param:
            raise ValueError("required_param cannot be empty")
        
        # Main logic here
        result = {"status": "success", "data": required_param}
        
        execution_time = time.time() - start_time
        logger.info(f"Function completed in {execution_time:.2f} seconds")
        
        return result
        
    except Exception as e:
        logger.error(f"Function failed: {str(e)}", exc_info=True)
        raise
```

### Architecture Decision Guidelines

#### When to Use Parallel Processing
- **CPU-Bound Tasks**: Parameter optimization, strategy backtesting
- **Independent Operations**: Multiple strategy evaluations, data preprocessing
- **Large Datasets**: When processing time > 10 seconds sequentially
- **Memory Allows**: When parallel processing won't exceed 80% memory usage

#### When to Use Single-Threading
- **I/O-Bound Tasks**: File reading, network requests
- **Small Datasets**: When parallel overhead exceeds benefits
- **Memory Constraints**: When parallel processing would exceed memory limits
- **Sequential Dependencies**: When operations must be performed in order

#### Memory Management Decisions
```python
# AI should suggest chunking for large datasets
def process_large_dataset(data: pd.DataFrame, chunk_size: int = 10000) -> pd.DataFrame:
    """Process large dataset in chunks to manage memory usage."""
    if len(data) < chunk_size:
        return process_single_chunk(data)
    
    results = []
    for chunk in chunk_generator(data, chunk_size):
        processed_chunk = process_single_chunk(chunk)
        results.append(processed_chunk)
        
        # Explicit garbage collection for memory management
        if len(results) % 10 == 0:
            gc.collect()
    
    return pd.concat(results, ignore_index=True)
```

### Strategy Implementation Guidelines

#### Base Strategy Requirements
When generating strategy code, always:
1. Inherit from `BaseStrategy` class
2. Implement required abstract methods
3. Include parameter validation
4. Add performance metrics calculation
5. Implement proper signal generation

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import pandas as pd

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies."""
    
    def __init__(self, params: Dict[str, Any]):
        self.params = self._validate_params(params)
        self.signals = pd.DataFrame()
        self.performance_metrics = {}
    
    @abstractmethod
    def _validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate strategy parameters."""
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate trading signals based on data."""
        pass
    
    @abstractmethod
    def calculate_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate strategy performance metrics."""
        pass
```

### Optimization Algorithm Guidelines

#### Parameter Grid Generation
- Always validate parameter ranges
- Implement memory-efficient grid generation
- Support both linear and logarithmic spacing
- Include parameter combination validation

#### Parallel Optimization Rules
- Distribute work evenly across available cores
- Implement progress tracking and reporting
- Handle worker process failures gracefully
- Aggregate results efficiently

```python
# Template for parallel optimization
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Tuple, Dict, Any

def parallel_optimize(
    strategy_class: Type[BaseStrategy],
    data: pd.DataFrame,
    param_combinations: List[Dict[str, Any]],
    max_workers: Optional[int] = None
) -> List[OptimizationResult]:
    """Run optimization in parallel across multiple processes."""
    
    if max_workers is None:
        max_workers = min(32, len(param_combinations))  # Use available cores
    
    logger.info(f"Starting parallel optimization with {max_workers} workers")
    
    results = []
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_params = {
            executor.submit(single_backtest, strategy_class, data, params): params
            for params in param_combinations
        }
        
        # Collect results with progress tracking
        completed = 0
        for future in as_completed(future_to_params):
            try:
                result = future.result()
                results.append(result)
                completed += 1
                
                if completed % 100 == 0:
                    logger.info(f"Completed {completed}/{len(param_combinations)} optimizations")
                    
            except Exception as e:
                params = future_to_params[future]
                logger.error(f"Optimization failed for params {params}: {str(e)}")
    
    return results
```

### Data Handling Rules

#### Data Validation Requirements
- Always validate data completeness
- Check for missing values and handle appropriately
- Verify data types and ranges
- Implement data quality metrics

#### Memory-Efficient Data Processing
- Use generators for large datasets
- Implement data chunking strategies
- Monitor memory usage during processing
- Clean up intermediate data structures

### Error Handling Standards

#### Exception Hierarchy
```python
class BacktesterError(Exception):
    """Base exception for backtester operations."""
    pass

class DataError(BacktesterError):
    """Data-related errors."""
    pass

class StrategyError(BacktesterError):
    """Strategy implementation errors."""
    pass

class OptimizationError(BacktesterError):
    """Optimization process errors."""
    pass

class ResourceError(BacktesterError):
    """System resource errors."""
    pass
```

#### Error Recovery Strategies
- Implement automatic retry mechanisms for transient failures
- Provide graceful degradation when resources are constrained
- Log detailed error information for debugging
- Maintain system stability during partial failures

### Performance Monitoring Requirements

#### System Metrics to Track
- CPU utilization per core
- Memory usage (RSS and virtual)
- I/O operations and throughput
- Process execution times
- Queue sizes and processing rates

#### Performance Alerting
```python
import psutil
import logging
from typing import Dict, Any

def monitor_system_resources() -> Dict[str, Any]:
    """Monitor system resources and alert on thresholds."""
    metrics = {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_io': psutil.disk_io_counters(),
        'network_io': psutil.net_io_counters()
    }
    
    # Alert on high resource usage
    if metrics['memory_percent'] > 85:
        logger.warning(f"High memory usage: {metrics['memory_percent']:.1f}%")
    
    if metrics['cpu_percent'] > 95:
        logger.warning(f"High CPU usage: {metrics['cpu_percent']:.1f}%")
    
    return metrics
```

### Testing Requirements for AI-Generated Code

#### Unit Test Generation
When generating code, also provide corresponding unit tests:

```python
import pytest
from unittest.mock import Mock, patch

class TestGeneratedFunction:
    """Test cases for AI-generated function."""
    
    def test_normal_operation(self):
        """Test function works with valid inputs."""
        result = generated_function("valid_input")
        assert result is not None
        assert result['status'] == 'success'
    
    def test_invalid_input(self):
        """Test function handles invalid inputs."""
        with pytest.raises(ValueError):
            generated_function("")
    
    def test_performance_requirements(self):
        """Test function meets performance requirements."""
        start_time = time.time()
        result = generated_function("test_input")
        execution_time = time.time() - start_time
        
        assert execution_time < 1.0  # Should complete within 1 second
        assert result is not None
```

### Configuration Management Rules

#### Environment-Specific Configurations
- Always externalize configuration parameters
- Support development, testing, and production environments
- Validate configuration on application startup
- Provide sensible defaults for optional parameters

#### Hardware-Specific Tuning
```yaml
# config/hardware_optimization.yaml
hardware:
  cpu_cores: 32
  memory_gb: 96
  
optimization:
  max_workers: 28  # Leave 4 cores for system
  chunk_size_mb: 512  # Based on available memory
  memory_limit_percent: 80
  
parallel_processing:
  strategy_optimization:
    workers: 16
    memory_per_worker_mb: 4096
  data_processing:
    workers: 8
    chunk_size: 100000
```

### Documentation Requirements

#### API Documentation
- Generate comprehensive API documentation
- Include usage examples for all public functions
- Document performance characteristics
- Provide troubleshooting guides

#### Performance Documentation
- Document expected memory usage
- Provide CPU utilization guidelines
- Include scaling recommendations
- Document optimization best practices

### Security Considerations

#### Data Protection
- Never log sensitive trading data
- Implement secure configuration management
- Validate all external inputs
- Use secure communication protocols

#### Resource Protection
- Implement resource usage limits
- Prevent memory exhaustion attacks
- Monitor for abnormal resource consumption
- Implement graceful shutdown procedures

### Continuous Improvement Guidelines

#### Performance Optimization
- Profile code regularly to identify bottlenecks
- Implement performance regression testing
- Monitor production performance metrics
- Optimize based on real-world usage patterns

#### Code Quality Maintenance
- Regular code reviews for AI-generated code
- Automated testing for all new features
- Performance benchmarking for critical paths
- Documentation updates with code changes

These AI behavior rules ensure that all generated code and architectural decisions align with the project's performance goals, quality standards, and hardware capabilities while maintaining the integrity of the backtesting process.