"""Base strategy framework for cryptocurrency backtesting system.

This module provides the foundation for implementing trading strategies
with event-driven architecture and comprehensive performance tracking.
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from collections import defaultdict, deque

from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class OrderType(Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order sides."""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order status."""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL = "partial"


class PositionSide(Enum):
    """Position sides."""
    LONG = "long"
    SHORT = "short"
    FLAT = "flat"


@dataclass
class Order:
    """Order representation."""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    filled_price: Optional[float] = None
    commission: float = 0.0
    strategy_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Position:
    """Position representation."""
    symbol: str
    side: PositionSide
    quantity: float
    entry_price: float
    current_price: float
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    commission: float = 0.0
    entry_time: Optional[datetime] = None
    last_update: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def market_value(self) -> float:
        """Current market value of position."""
        return abs(self.quantity) * self.current_price
    
    @property
    def is_long(self) -> bool:
        """Check if position is long."""
        return self.side == PositionSide.LONG
    
    @property
    def is_short(self) -> bool:
        """Check if position is short."""
        return self.side == PositionSide.SHORT
    
    @property
    def is_flat(self) -> bool:
        """Check if position is flat."""
        return self.side == PositionSide.FLAT or self.quantity == 0


@dataclass
class Trade:
    """Trade representation."""
    id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float = 0.0
    order_id: Optional[str] = None
    strategy_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketData:
    """Market data representation."""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def ohlc(self) -> Tuple[float, float, float, float]:
        """Get OHLC tuple."""
        return (self.open, self.high, self.low, self.close)
    
    @property
    def typical_price(self) -> float:
        """Calculate typical price (HLC/3)."""
        return (self.high + self.low + self.close) / 3
    
    @property
    def weighted_price(self) -> float:
        """Calculate weighted price (OHLC/4)."""
        return (self.open + self.high + self.low + self.close) / 4


@dataclass
class StrategyState:
    """Strategy state for persistence."""
    strategy_id: str
    timestamp: datetime
    positions: Dict[str, Position]
    orders: Dict[str, Order]
    cash: float
    portfolio_value: float
    custom_state: Dict[str, Any] = field(default_factory=dict)


class StrategyMetrics:
    """Strategy performance metrics calculator."""
    
    def __init__(self):
        self.trades: List[Trade] = []
        self.portfolio_values: List[Tuple[datetime, float]] = []
        self.drawdowns: List[Tuple[datetime, float]] = []
        self.returns: List[float] = []
        
    def add_trade(self, trade: Trade) -> None:
        """Add trade to metrics."""
        self.trades.append(trade)
    
    def add_portfolio_value(self, timestamp: datetime, value: float) -> None:
        """Add portfolio value snapshot."""
        self.portfolio_values.append((timestamp, value))
        
        # Calculate returns
        if len(self.portfolio_values) > 1:
            prev_value = self.portfolio_values[-2][1]
            if prev_value > 0:
                return_pct = (value - prev_value) / prev_value
                self.returns.append(return_pct)
    
    def calculate_metrics(self) -> Dict[str, float]:
        """Calculate comprehensive performance metrics."""
        if not self.portfolio_values:
            return {}
        
        # Basic metrics
        initial_value = self.portfolio_values[0][1]
        final_value = self.portfolio_values[-1][1]
        total_return = (final_value - initial_value) / initial_value if initial_value > 0 else 0
        
        # Trade metrics
        winning_trades = [t for t in self.trades if self._calculate_trade_pnl(t) > 0]
        losing_trades = [t for t in self.trades if self._calculate_trade_pnl(t) < 0]
        
        win_rate = len(winning_trades) / len(self.trades) if self.trades else 0
        
        # Return metrics
        returns_array = np.array(self.returns) if self.returns else np.array([0])
        
        # Risk metrics
        volatility = np.std(returns_array) * np.sqrt(252) if len(returns_array) > 1 else 0
        sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
        
        # Drawdown metrics
        max_drawdown = self._calculate_max_drawdown()
        
        return {
            'total_return': total_return,
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_portfolio_value': final_value
        }
    
    def _calculate_trade_pnl(self, trade: Trade) -> float:
        """Calculate trade P&L (simplified)."""
        # This is a simplified calculation
        # In practice, you'd need to track entry/exit prices
        return 0.0  # Placeholder
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown."""
        if len(self.portfolio_values) < 2:
            return 0.0
        
        values = [v[1] for v in self.portfolio_values]
        peak = values[0]
        max_dd = 0.0
        
        for value in values[1:]:
            if value > peak:
                peak = value
            else:
                drawdown = (peak - value) / peak
                max_dd = max(max_dd, drawdown)
        
        return max_dd


class BaseStrategy(ABC):
    """Base class for all trading strategies."""
    
    def __init__(
        self,
        strategy_id: str,
        symbols: List[str],
        initial_cash: float = 100000.0,
        commission_rate: float = 0.001,
        slippage: float = 0.0001
    ):
        """Initialize base strategy.
        
        Args:
            strategy_id: Unique strategy identifier
            symbols: List of symbols to trade
            initial_cash: Initial cash amount
            commission_rate: Commission rate (0.001 = 0.1%)
            slippage: Slippage rate
        """
        self.strategy_id = strategy_id
        self.symbols = symbols
        self.initial_cash = initial_cash
        self.commission_rate = commission_rate
        self.slippage = slippage
        
        # State management
        self.cash = initial_cash
        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.trades: List[Trade] = []
        
        # Data management
        self.current_data: Dict[str, MarketData] = {}
        self.historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Performance tracking
        self.metrics = StrategyMetrics()
        
        # Threading
        self._lock = threading.Lock()
        
        # Logging
        self.logger = get_logger(f"strategy.{strategy_id}")
        
        # Configuration
        self.config = get_config()
        
        # Strategy-specific state
        self.custom_state: Dict[str, Any] = {}
        
        # Event handlers
        self._event_handlers: Dict[str, List[callable]] = defaultdict(list)
        
        self.logger.info(f"Initialized strategy {strategy_id} with {len(symbols)} symbols")
    
    @abstractmethod
    def on_data(self, data: MarketData) -> None:
        """Handle new market data.
        
        Args:
            data: Market data
        """
        pass
    
    @abstractmethod
    def on_order_filled(self, order: Order, trade: Trade) -> None:
        """Handle order fill event.
        
        Args:
            order: Filled order
            trade: Resulting trade
        """
        pass
    
    def on_order_cancelled(self, order: Order) -> None:
        """Handle order cancellation.
        
        Args:
            order: Cancelled order
        """
        self.logger.info(f"Order {order.id} cancelled")
    
    def on_order_rejected(self, order: Order, reason: str) -> None:
        """Handle order rejection.
        
        Args:
            order: Rejected order
            reason: Rejection reason
        """
        self.logger.warning(f"Order {order.id} rejected: {reason}")
    
    def on_position_update(self, position: Position) -> None:
        """Handle position update.
        
        Args:
            position: Updated position
        """
        pass
    
    def on_start(self) -> None:
        """Called when strategy starts."""
        self.logger.info(f"Strategy {self.strategy_id} started")
    
    def on_stop(self) -> None:
        """Called when strategy stops."""
        self.logger.info(f"Strategy {self.strategy_id} stopped")
    
    def create_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: float,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Order:
        """Create a new order.
        
        Args:
            symbol: Trading symbol
            side: Order side (buy/sell)
            order_type: Order type
            quantity: Order quantity
            price: Limit price (for limit orders)
            stop_price: Stop price (for stop orders)
            metadata: Additional metadata
            
        Returns:
            Created order
        """
        order_id = f"{self.strategy_id}_{symbol}_{datetime.now().timestamp()}"
        
        order = Order(
            id=order_id,
            symbol=symbol,
            side=side,
            type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            timestamp=datetime.now(),
            strategy_id=self.strategy_id,
            metadata=metadata or {}
        )
        
        with self._lock:
            self.orders[order_id] = order
        
        self.logger.info(
            f"Created {order_type.value} {side.value} order for {quantity} {symbol} "
            f"at {price if price else 'market'}"
        )
        
        return order
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order.
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if successful
        """
        with self._lock:
            if order_id in self.orders:
                order = self.orders[order_id]
                if order.status == OrderStatus.PENDING:
                    order.status = OrderStatus.CANCELLED
                    self.on_order_cancelled(order)
                    return True
        
        return False
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get current position for symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Position or None
        """
        return self.positions.get(symbol)
    
    def get_portfolio_value(self) -> float:
        """Calculate current portfolio value.
        
        Returns:
            Total portfolio value
        """
        total_value = self.cash
        
        for symbol, position in self.positions.items():
            if not position.is_flat:
                total_value += position.market_value
        
        return total_value
    
    def get_available_cash(self) -> float:
        """Get available cash for trading.
        
        Returns:
            Available cash amount
        """
        # Account for pending orders
        reserved_cash = 0.0
        
        for order in self.orders.values():
            if order.status == OrderStatus.PENDING and order.side == OrderSide.BUY:
                if order.type == OrderType.MARKET:
                    # Estimate using current price
                    current_price = self.get_current_price(order.symbol)
                    if current_price:
                        reserved_cash += order.quantity * current_price
                elif order.price:
                    reserved_cash += order.quantity * order.price
        
        return max(0, self.cash - reserved_cash)
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Current price or None
        """
        data = self.current_data.get(symbol)
        return data.close if data else None
    
    def get_historical_data(self, symbol: str, periods: int = 100) -> List[MarketData]:
        """Get historical data for symbol.
        
        Args:
            symbol: Trading symbol
            periods: Number of periods to return
            
        Returns:
            List of historical market data
        """
        historical = self.historical_data.get(symbol, deque())
        return list(historical)[-periods:]
    
    def update_data(self, data: MarketData) -> None:
        """Update market data.
        
        Args:
            data: New market data
        """
        with self._lock:
            # Update current data
            self.current_data[data.symbol] = data
            
            # Add to historical data
            self.historical_data[data.symbol].append(data)
            
            # Update positions with current prices
            if data.symbol in self.positions:
                position = self.positions[data.symbol]
                position.current_price = data.close
                position.last_update = data.timestamp
                
                # Calculate unrealized P&L
                if not position.is_flat:
                    if position.is_long:
                        position.unrealized_pnl = (data.close - position.entry_price) * position.quantity
                    else:
                        position.unrealized_pnl = (position.entry_price - data.close) * abs(position.quantity)
                
                self.on_position_update(position)
        
        # Update portfolio value
        portfolio_value = self.get_portfolio_value()
        self.metrics.add_portfolio_value(data.timestamp, portfolio_value)
        
        # Call strategy implementation
        self.on_data(data)
    
    def execute_trade(
        self,
        order: Order,
        fill_price: float,
        fill_quantity: Optional[float] = None
    ) -> Trade:
        """Execute a trade from an order.
        
        Args:
            order: Order to execute
            fill_price: Fill price
            fill_quantity: Fill quantity (defaults to order quantity)
            
        Returns:
            Executed trade
        """
        if fill_quantity is None:
            fill_quantity = order.quantity
        
        # Apply slippage
        if order.side == OrderSide.BUY:
            fill_price *= (1 + self.slippage)
        else:
            fill_price *= (1 - self.slippage)
        
        # Calculate commission
        commission = fill_quantity * fill_price * self.commission_rate
        
        # Create trade
        trade_id = f"trade_{order.id}_{datetime.now().timestamp()}"
        trade = Trade(
            id=trade_id,
            symbol=order.symbol,
            side=order.side,
            quantity=fill_quantity,
            price=fill_price,
            timestamp=datetime.now(),
            commission=commission,
            order_id=order.id,
            strategy_id=self.strategy_id
        )
        
        with self._lock:
            # Update order
            order.filled_quantity += fill_quantity
            order.filled_price = fill_price
            order.commission += commission
            
            if order.filled_quantity >= order.quantity:
                order.status = OrderStatus.FILLED
            else:
                order.status = OrderStatus.PARTIAL
            
            # Update cash
            if order.side == OrderSide.BUY:
                self.cash -= (fill_quantity * fill_price + commission)
            else:
                self.cash += (fill_quantity * fill_price - commission)
            
            # Update position
            self._update_position(trade)
            
            # Add to trades
            self.trades.append(trade)
            self.metrics.add_trade(trade)
        
        self.logger.info(
            f"Executed trade: {trade.side.value} {trade.quantity} {trade.symbol} "
            f"at {trade.price:.6f} (commission: {commission:.6f})"
        )
        
        # Call event handler
        self.on_order_filled(order, trade)
        
        return trade
    
    def _update_position(self, trade: Trade) -> None:
        """Update position based on trade.
        
        Args:
            trade: Executed trade
        """
        symbol = trade.symbol
        
        if symbol not in self.positions:
            # Create new position
            side = PositionSide.LONG if trade.side == OrderSide.BUY else PositionSide.SHORT
            quantity = trade.quantity if trade.side == OrderSide.BUY else -trade.quantity
            
            self.positions[symbol] = Position(
                symbol=symbol,
                side=side,
                quantity=quantity,
                entry_price=trade.price,
                current_price=trade.price,
                commission=trade.commission,
                entry_time=trade.timestamp
            )
        else:
            # Update existing position
            position = self.positions[symbol]
            
            if trade.side == OrderSide.BUY:
                new_quantity = position.quantity + trade.quantity
            else:
                new_quantity = position.quantity - trade.quantity
            
            if new_quantity == 0:
                # Position closed
                position.side = PositionSide.FLAT
                position.quantity = 0
            elif new_quantity > 0:
                # Long position
                if position.quantity <= 0:  # Was short or flat
                    position.entry_price = trade.price
                    position.entry_time = trade.timestamp
                else:  # Adding to long
                    # Calculate weighted average entry price
                    total_cost = (position.quantity * position.entry_price + 
                                trade.quantity * trade.price)
                    position.entry_price = total_cost / new_quantity
                
                position.side = PositionSide.LONG
                position.quantity = new_quantity
            else:
                # Short position
                if position.quantity >= 0:  # Was long or flat
                    position.entry_price = trade.price
                    position.entry_time = trade.timestamp
                else:  # Adding to short
                    # Calculate weighted average entry price
                    total_cost = (abs(position.quantity) * position.entry_price + 
                                trade.quantity * trade.price)
                    position.entry_price = total_cost / abs(new_quantity)
                
                position.side = PositionSide.SHORT
                position.quantity = new_quantity
            
            position.commission += trade.commission
            position.current_price = trade.price
            position.last_update = trade.timestamp
    
    def get_state(self) -> StrategyState:
        """Get current strategy state.
        
        Returns:
            Strategy state
        """
        return StrategyState(
            strategy_id=self.strategy_id,
            timestamp=datetime.now(),
            positions=self.positions.copy(),
            orders=self.orders.copy(),
            cash=self.cash,
            portfolio_value=self.get_portfolio_value(),
            custom_state=self.custom_state.copy()
        )
    
    def restore_state(self, state: StrategyState) -> None:
        """Restore strategy state.
        
        Args:
            state: Strategy state to restore
        """
        with self._lock:
            self.positions = state.positions.copy()
            self.orders = state.orders.copy()
            self.cash = state.cash
            self.custom_state = state.custom_state.copy()
        
        self.logger.info(f"Restored strategy state from {state.timestamp}")
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics.
        
        Returns:
            Performance metrics dictionary
        """
        return self.metrics.calculate_metrics()
    
    def add_event_handler(self, event_type: str, handler: callable) -> None:
        """Add event handler.
        
        Args:
            event_type: Event type
            handler: Handler function
        """
        self._event_handlers[event_type].append(handler)
    
    def emit_event(self, event_type: str, *args, **kwargs) -> None:
        """Emit event to handlers.
        
        Args:
            event_type: Event type
            *args: Event arguments
            **kwargs: Event keyword arguments
        """
        for handler in self._event_handlers.get(event_type, []):
            try:
                handler(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"Error in event handler {handler}: {e}")
    
    def __str__(self) -> str:
        """String representation."""
        return f"Strategy({self.strategy_id}, symbols={self.symbols}, cash={self.cash:.2f})"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return (
            f"BaseStrategy(strategy_id='{self.strategy_id}', "
            f"symbols={self.symbols}, cash={self.cash:.2f}, "
            f"positions={len(self.positions)}, orders={len(self.orders)})"
        )