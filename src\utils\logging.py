"""Logging configuration and utilities.

This module provides centralized logging configuration for the crypto
backtesting system with support for file rotation, structured logging,
and performance monitoring.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime
import threading
from contextlib import contextmanager
import time

from loguru import logger as loguru_logger
from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install as install_rich_traceback


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            Formatted log message
        """
        # Create base log entry
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add thread information
        if hasattr(record, 'thread'):
            log_entry['thread_id'] = record.thread
            log_entry['thread_name'] = getattr(record, 'threadName', 'Unknown')
        
        # Add process information
        if hasattr(record, 'process'):
            log_entry['process_id'] = record.process
        
        # Add exception information
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }
        
        # Add custom fields
        for key, value in record.__dict__.items():
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'exc_info',
                'exc_text', 'stack_info'
            }:
                log_entry[key] = value
        
        return json.dumps(log_entry, default=str)


class PerformanceFilter(logging.Filter):
    """Filter for performance-related log messages."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter log records based on performance criteria.
        
        Args:
            record: Log record to filter
            
        Returns:
            True if record should be logged
        """
        # Add performance metrics if available
        if hasattr(record, 'duration'):
            record.performance = True
        
        return True


class LoggerManager:
    """Centralized logger management."""
    
    def __init__(self):
        """Initialize logger manager."""
        self._loggers: Dict[str, logging.Logger] = {}
        self._configured = False
        self._lock = threading.Lock()
        
        # Install rich traceback for better error display
        install_rich_traceback(show_locals=True)
    
    def configure(
        self,
        level: str = "INFO",
        format_string: Optional[str] = None,
        file_path: Optional[str] = None,
        max_file_size: str = "100MB",
        backup_count: int = 5,
        structured: bool = False,
        console_output: bool = True,
        rich_formatting: bool = True
    ) -> None:
        """Configure logging system.
        
        Args:
            level: Logging level
            format_string: Custom format string
            file_path: Path to log file
            max_file_size: Maximum log file size
            backup_count: Number of backup files to keep
            structured: Whether to use structured JSON logging
            console_output: Whether to output to console
            rich_formatting: Whether to use rich formatting
        """
        with self._lock:
            if self._configured:
                return
            
            # Set root logger level
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, level.upper()))
            
            # Clear existing handlers
            root_logger.handlers.clear()
            
            # Configure console handler
            if console_output:
                if rich_formatting:
                    console_handler = RichHandler(
                        console=Console(stderr=True),
                        show_time=True,
                        show_level=True,
                        show_path=True,
                        markup=True,
                        rich_tracebacks=True
                    )
                else:
                    console_handler = logging.StreamHandler(sys.stderr)
                
                if structured:
                    console_handler.setFormatter(StructuredFormatter())
                else:
                    format_str = format_string or (
                        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    )
                    console_handler.setFormatter(logging.Formatter(format_str))
                
                console_handler.addFilter(PerformanceFilter())
                root_logger.addHandler(console_handler)
            
            # Configure file handler
            if file_path:
                # Ensure log directory exists
                log_file = Path(file_path)
                log_file.parent.mkdir(parents=True, exist_ok=True)
                
                # Parse file size
                if max_file_size.endswith('MB'):
                    max_bytes = int(max_file_size[:-2]) * 1024 * 1024
                elif max_file_size.endswith('GB'):
                    max_bytes = int(max_file_size[:-2]) * 1024 * 1024 * 1024
                else:
                    max_bytes = int(max_file_size)
                
                file_handler = logging.handlers.RotatingFileHandler(
                    filename=file_path,
                    maxBytes=max_bytes,
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                
                if structured:
                    file_handler.setFormatter(StructuredFormatter())
                else:
                    format_str = format_string or (
                        "%(asctime)s - %(name)s - %(levelname)s - "
                        "%(module)s:%(funcName)s:%(lineno)d - %(message)s"
                    )
                    file_handler.setFormatter(logging.Formatter(format_str))
                
                file_handler.addFilter(PerformanceFilter())
                root_logger.addHandler(file_handler)
            
            # Configure loguru for advanced features
            loguru_logger.remove()  # Remove default handler
            
            if console_output and rich_formatting:
                loguru_logger.add(
                    sys.stderr,
                    level=level.upper(),
                    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                           "<level>{level: <8}</level> | "
                           "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                           "<level>{message}</level>",
                    colorize=True
                )
            
            if file_path:
                loguru_logger.add(
                    file_path.replace('.log', '_loguru.log'),
                    level=level.upper(),
                    rotation=max_file_size,
                    retention=backup_count,
                    compression="zip",
                    serialize=structured
                )
            
            self._configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger instance.
        
        Args:
            name: Logger name
            
        Returns:
            Logger instance
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            
            # Add custom methods
            logger.performance = lambda msg, **kwargs: self._log_performance(
                logger, msg, **kwargs
            )
            logger.timing = lambda func: self._timing_decorator(logger, func)
            
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def _log_performance(
        self,
        logger: logging.Logger,
        message: str,
        duration: Optional[float] = None,
        **kwargs
    ) -> None:
        """Log performance-related message.
        
        Args:
            logger: Logger instance
            message: Log message
            duration: Operation duration in seconds
            **kwargs: Additional context
        """
        extra = {'performance': True}
        if duration is not None:
            extra['duration'] = duration
        extra.update(kwargs)
        
        logger.info(message, extra=extra)
    
    def _timing_decorator(self, logger: logging.Logger, func):
        """Decorator for timing function execution.
        
        Args:
            logger: Logger instance
            func: Function to time
            
        Returns:
            Decorated function
        """
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.performance(
                    f"Function {func.__name__} completed",
                    duration=duration,
                    function=func.__name__,
                    args_count=len(args),
                    kwargs_count=len(kwargs)
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    f"Function {func.__name__} failed after {duration:.3f}s: {e}",
                    extra={
                        'duration': duration,
                        'function': func.__name__,
                        'error': str(e)
                    }
                )
                raise
        return wrapper


# Global logger manager instance
_logger_manager: Optional[LoggerManager] = None


def configure_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    file_path: Optional[str] = None,
    max_file_size: str = "100MB",
    backup_count: int = 5,
    structured: bool = False,
    console_output: bool = True,
    rich_formatting: bool = True
) -> None:
    """Configure global logging system.
    
    Args:
        level: Logging level
        format_string: Custom format string
        file_path: Path to log file
        max_file_size: Maximum log file size
        backup_count: Number of backup files to keep
        structured: Whether to use structured JSON logging
        console_output: Whether to output to console
        rich_formatting: Whether to use rich formatting
    """
    global _logger_manager
    
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    
    _logger_manager.configure(
        level=level,
        format_string=format_string,
        file_path=file_path,
        max_file_size=max_file_size,
        backup_count=backup_count,
        structured=structured,
        console_output=console_output,
        rich_formatting=rich_formatting
    )


def get_logger(name: str) -> logging.Logger:
    """Get logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    global _logger_manager
    
    if _logger_manager is None:
        _logger_manager = LoggerManager()
        # Configure with defaults if not already configured
        _logger_manager.configure()
    
    return _logger_manager.get_logger(name)


@contextmanager
def log_performance(logger: logging.Logger, operation: str, **context):
    """Context manager for logging operation performance.
    
    Args:
        logger: Logger instance
        operation: Operation description
        **context: Additional context to log
    
    Yields:
        Dictionary for adding runtime context
    """
    start_time = time.time()
    runtime_context = {}
    
    try:
        logger.info(f"Starting {operation}", extra=context)
        yield runtime_context
        
        duration = time.time() - start_time
        logger.performance(
            f"Completed {operation}",
            duration=duration,
            operation=operation,
            **context,
            **runtime_context
        )
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            f"Failed {operation} after {duration:.3f}s: {e}",
            extra={
                'duration': duration,
                'operation': operation,
                'error': str(e),
                **context,
                **runtime_context
            }
        )
        raise


def timing(logger: Optional[logging.Logger] = None):
    """Decorator for timing function execution.
    
    Args:
        logger: Logger instance (uses function name if None)
        
    Returns:
        Decorator function
    """
    def decorator(func):
        nonlocal logger
        if logger is None:
            logger = get_logger(func.__module__)
        
        return _logger_manager._timing_decorator(logger, func) if _logger_manager else func
    
    return decorator