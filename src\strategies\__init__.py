"""Trading strategies module for cryptocurrency backtesting system.

This module provides a comprehensive framework for implementing and managing
trading strategies with support for multiple strategy types and parameter optimization.

Classes:
    BaseStrategy: Abstract base class for all trading strategies
    MovingAverageCrossover: Simple moving average crossover strategy
    RSIStrategy: RSI-based trading strategy
    BollingerBandsStrategy: Bollinger Bands trading strategy
    MACDStrategy: MACD-based trading strategy
    StrategyManager: Manage multiple strategies and their parameters

Functions:
    register_strategy: Register a new strategy class
    get_strategy: Get strategy class by name
    list_strategies: List all available strategies

Example:
    >>> from src.strategies import MovingAverageCrossover, StrategyManager
    >>> 
    >>> # Create strategy instance
    >>> strategy = MovingAverageCrossover(fast_period=20, slow_period=50)
    >>> 
    >>> # Use strategy manager for multiple strategies
    >>> manager = StrategyManager()
    >>> manager.add_strategy('ma_cross', MovingAverageCrossover)
    >>> manager.add_strategy('rsi', RSIStrategy)
    >>> 
    >>> # Get strategy parameters
    >>> params = strategy.get_parameters()
    >>> print(f"Strategy parameters: {params}")
"""

# Import base classes and implementations
try:
    from .base import BaseStrategy
    from .ma_crossover import MovingAverage<PERSON>rossover
    from .rsi_strategy import RSIStrategy
    from .bollinger_bands import BollingerBandsStrategy
    from .macd_strategy import MACDStrategy
    from .manager import StrategyManager
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Strategy registry for dynamic loading
_STRATEGY_REGISTRY = {}

def register_strategy(name: str, strategy_class: type) -> None:
    """Register a strategy class for dynamic loading.
    
    Args:
        name: Strategy name identifier
        strategy_class: Strategy class to register
    """
    _STRATEGY_REGISTRY[name] = strategy_class

def get_strategy(name: str) -> type:
    """Get strategy class by name.
    
    Args:
        name: Strategy name identifier
        
    Returns:
        Strategy class
        
    Raises:
        KeyError: If strategy not found
    """
    if name not in _STRATEGY_REGISTRY:
        raise KeyError(f"Strategy '{name}' not found. Available: {list(_STRATEGY_REGISTRY.keys())}")
    return _STRATEGY_REGISTRY[name]

def list_strategies() -> list[str]:
    """List all available strategy names.
    
    Returns:
        List of strategy names
    """
    return list(_STRATEGY_REGISTRY.keys())

# Default strategy parameters
DEFAULT_PARAMETERS = {
    'ma_crossover': {
        'fast_period': 20,
        'slow_period': 50,
        'signal_threshold': 0.001
    },
    'rsi': {
        'period': 14,
        'overbought': 70,
        'oversold': 30
    },
    'bollinger_bands': {
        'period': 20,
        'std_dev': 2.0,
        'squeeze_threshold': 0.1
    },
    'macd': {
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9
    }
}

# Strategy performance thresholds
MIN_SHARPE_RATIO = 1.0
MIN_WIN_RATE = 0.4
MAX_DRAWDOWN = 0.2

# Export public API
__all__ = [
    "BaseStrategy",
    "MovingAverageCrossover",
    "RSIStrategy",
    "BollingerBandsStrategy",
    "MACDStrategy",
    "StrategyManager",
    "register_strategy",
    "get_strategy",
    "list_strategies",
    "DEFAULT_PARAMETERS",
    "MIN_SHARPE_RATIO",
    "MIN_WIN_RATE",
    "MAX_DRAWDOWN",
]