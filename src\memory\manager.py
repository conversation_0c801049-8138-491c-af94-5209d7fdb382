"""Memory management module for cryptocurrency backtesting system.

This module provides efficient memory management, data caching, and memory optimization
for large-scale backtesting operations with limited memory resources.
"""

import gc
import os
import sys
import threading
import time
import weakref
from typing import Dict, List, Optional, Any, Tuple, Union, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import OrderedDict, defaultdict
from pathlib import Path
import pickle
import mmap
import tempfile
import shutil

import pandas as pd
import numpy as np
import psutil
from threading import Lock, RLock

from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class CachePolicy(Enum):
    """Cache eviction policies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    FIFO = "fifo"  # First In, First Out
    TTL = "ttl"  # Time To Live
    SIZE_BASED = "size_based"  # Based on memory size


class MemoryPriority(Enum):
    """Memory priority levels."""
    CRITICAL = "critical"  # Never evict
    HIGH = "high"  # Evict only under pressure
    MEDIUM = "medium"  # Normal eviction
    LOW = "low"  # Evict first


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    data: Any
    size_bytes: int
    access_count: int = 0
    last_access: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    priority: MemoryPriority = MemoryPriority.MEDIUM
    ttl_seconds: Optional[int] = None
    pinned: bool = False  # Prevent eviction
    
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl_seconds is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl_seconds
    
    def touch(self) -> None:
        """Update access metadata."""
        self.access_count += 1
        self.last_access = datetime.now()


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    total_memory_gb: float
    available_memory_gb: float
    used_memory_gb: float
    cache_memory_gb: float
    cache_entries: int
    cache_hit_rate: float
    gc_collections: int
    last_gc_time: datetime
    memory_pressure: float  # 0.0 to 1.0


class MemoryMonitor:
    """Monitors system memory usage."""
    
    def __init__(self, check_interval: float = 1.0):
        """Initialize memory monitor.
        
        Args:
            check_interval: Check interval in seconds
        """
        self.check_interval = check_interval
        self.logger = get_logger("memory_monitor")
        
        # Memory thresholds
        self.warning_threshold = 0.8  # 80%
        self.critical_threshold = 0.9  # 90%
        
        # Monitoring state
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        # Callbacks
        self._warning_callbacks: List[Callable] = []
        self._critical_callbacks: List[Callable] = []
        
        # Statistics
        self.stats_history: List[MemoryStats] = []
        self.max_history_size = 1000
    
    def start_monitoring(self) -> None:
        """Start memory monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("Memory monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop memory monitoring."""
        if not self._monitoring:
            return
        
        self._monitoring = False
        self._stop_event.set()
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("Memory monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while not self._stop_event.wait(self.check_interval):
            try:
                stats = self.get_memory_stats()
                self.stats_history.append(stats)
                
                # Trim history
                if len(self.stats_history) > self.max_history_size:
                    self.stats_history = self.stats_history[-self.max_history_size:]
                
                # Check thresholds
                if stats.memory_pressure >= self.critical_threshold:
                    self._trigger_critical_callbacks(stats)
                elif stats.memory_pressure >= self.warning_threshold:
                    self._trigger_warning_callbacks(stats)
                
            except Exception as e:
                self.logger.error(f"Memory monitoring error: {e}")
    
    def get_memory_stats(self, cache_manager: Optional['CacheManager'] = None) -> MemoryStats:
        """Get current memory statistics.
        
        Args:
            cache_manager: Optional cache manager for cache stats
            
        Returns:
            Memory statistics
        """
        # System memory
        memory = psutil.virtual_memory()
        total_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        used_gb = memory.used / (1024**3)
        
        # Cache statistics
        cache_memory_gb = 0.0
        cache_entries = 0
        cache_hit_rate = 0.0
        
        if cache_manager:
            cache_memory_gb = cache_manager.get_total_size() / (1024**3)
            cache_entries = len(cache_manager._cache)
            cache_hit_rate = cache_manager.get_hit_rate()
        
        # GC statistics
        gc_stats = gc.get_stats()
        gc_collections = sum(stat['collections'] for stat in gc_stats)
        
        return MemoryStats(
            total_memory_gb=total_gb,
            available_memory_gb=available_gb,
            used_memory_gb=used_gb,
            cache_memory_gb=cache_memory_gb,
            cache_entries=cache_entries,
            cache_hit_rate=cache_hit_rate,
            gc_collections=gc_collections,
            last_gc_time=datetime.now(),
            memory_pressure=1.0 - (available_gb / total_gb)
        )
    
    def add_warning_callback(self, callback: Callable[[MemoryStats], None]) -> None:
        """Add warning threshold callback."""
        self._warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback: Callable[[MemoryStats], None]) -> None:
        """Add critical threshold callback."""
        self._critical_callbacks.append(callback)
    
    def _trigger_warning_callbacks(self, stats: MemoryStats) -> None:
        """Trigger warning callbacks."""
        for callback in self._warning_callbacks:
            try:
                callback(stats)
            except Exception as e:
                self.logger.error(f"Warning callback error: {e}")
    
    def _trigger_critical_callbacks(self, stats: MemoryStats) -> None:
        """Trigger critical callbacks."""
        for callback in self._critical_callbacks:
            try:
                callback(stats)
            except Exception as e:
                self.logger.error(f"Critical callback error: {e}")


class CacheManager:
    """Intelligent cache manager with multiple eviction policies."""
    
    def __init__(
        self,
        max_size_gb: float = 8.0,
        policy: CachePolicy = CachePolicy.LRU,
        cleanup_threshold: float = 0.9,
        cleanup_target: float = 0.7
    ):
        """Initialize cache manager.
        
        Args:
            max_size_gb: Maximum cache size in GB
            policy: Cache eviction policy
            cleanup_threshold: Trigger cleanup at this usage ratio
            cleanup_target: Clean up to this usage ratio
        """
        self.max_size_bytes = int(max_size_gb * 1024**3)
        self.policy = policy
        self.cleanup_threshold = cleanup_threshold
        self.cleanup_target = cleanup_target
        
        self.logger = get_logger("cache_manager")
        
        # Cache storage
        self._cache: Dict[str, CacheEntry] = {}
        self._access_order: OrderedDict = OrderedDict()  # For LRU
        self._frequency_counter: Dict[str, int] = defaultdict(int)  # For LFU
        
        # Statistics
        self._hits = 0
        self._misses = 0
        self._evictions = 0
        self._current_size = 0
        
        # Threading
        self._lock = RLock()
        
        # Cleanup
        self._last_cleanup = datetime.now()
        self._cleanup_interval = timedelta(minutes=5)
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached data or None if not found
        """
        with self._lock:
            entry = self._cache.get(key)
            
            if entry is None:
                self._misses += 1
                return None
            
            # Check expiration
            if entry.is_expired():
                self._remove_entry(key)
                self._misses += 1
                return None
            
            # Update access metadata
            entry.touch()
            self._hits += 1
            
            # Update policy-specific data structures
            if self.policy == CachePolicy.LRU:
                self._access_order.move_to_end(key)
            elif self.policy == CachePolicy.LFU:
                self._frequency_counter[key] += 1
            
            return entry.data
    
    def put(
        self,
        key: str,
        data: Any,
        priority: MemoryPriority = MemoryPriority.MEDIUM,
        ttl_seconds: Optional[int] = None,
        pinned: bool = False
    ) -> bool:
        """Put item in cache.
        
        Args:
            key: Cache key
            data: Data to cache
            priority: Memory priority
            ttl_seconds: Time to live in seconds
            pinned: Whether to pin in memory
            
        Returns:
            True if successfully cached
        """
        # Calculate size
        size_bytes = self._calculate_size(data)
        
        # Check if item is too large
        if size_bytes > self.max_size_bytes:
            self.logger.warning(f"Item too large for cache: {size_bytes / 1024**2:.1f} MB")
            return False
        
        with self._lock:
            # Remove existing entry if present
            if key in self._cache:
                self._remove_entry(key)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                data=data,
                size_bytes=size_bytes,
                priority=priority,
                ttl_seconds=ttl_seconds,
                pinned=pinned
            )
            
            # Ensure space
            if not self._ensure_space(size_bytes):
                self.logger.warning(f"Could not make space for cache entry: {key}")
                return False
            
            # Add to cache
            self._cache[key] = entry
            self._current_size += size_bytes
            
            # Update policy-specific data structures
            if self.policy == CachePolicy.LRU:
                self._access_order[key] = True
            elif self.policy == CachePolicy.LFU:
                self._frequency_counter[key] = 1
            
            # Periodic cleanup
            self._maybe_cleanup()
            
            return True
    
    def remove(self, key: str) -> bool:
        """Remove item from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if item was removed
        """
        with self._lock:
            return self._remove_entry(key)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._frequency_counter.clear()
            self._current_size = 0
            
            # Force garbage collection
            gc.collect()
    
    def get_size(self) -> int:
        """Get current cache size in bytes."""
        return self._current_size
    
    def get_total_size(self) -> int:
        """Get total cache size in bytes (alias for get_size)."""
        return self.get_size()
    
    def get_hit_rate(self) -> float:
        """Get cache hit rate."""
        total = self._hits + self._misses
        return self._hits / total if total > 0 else 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            return {
                'entries': len(self._cache),
                'size_bytes': self._current_size,
                'size_mb': self._current_size / 1024**2,
                'max_size_bytes': self.max_size_bytes,
                'max_size_mb': self.max_size_bytes / 1024**2,
                'usage_ratio': self._current_size / self.max_size_bytes,
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': self.get_hit_rate(),
                'evictions': self._evictions,
                'policy': self.policy.value
            }
    
    def _calculate_size(self, data: Any) -> int:
        """Calculate approximate size of data.
        
        Args:
            data: Data to measure
            
        Returns:
            Size in bytes
        """
        try:
            if isinstance(data, pd.DataFrame):
                return data.memory_usage(deep=True).sum()
            elif isinstance(data, np.ndarray):
                return data.nbytes
            elif isinstance(data, (list, tuple)):
                return sum(sys.getsizeof(item) for item in data)
            elif isinstance(data, dict):
                return sum(sys.getsizeof(k) + sys.getsizeof(v) for k, v in data.items())
            else:
                return sys.getsizeof(data)
        except Exception:
            # Fallback to pickle size
            try:
                return len(pickle.dumps(data))
            except Exception:
                return sys.getsizeof(data)
    
    def _ensure_space(self, required_bytes: int) -> bool:
        """Ensure sufficient space for new entry.
        
        Args:
            required_bytes: Required space in bytes
            
        Returns:
            True if space was ensured
        """
        # Check if we have enough space
        if self._current_size + required_bytes <= self.max_size_bytes:
            return True
        
        # Calculate how much space we need to free
        target_size = int(self.max_size_bytes * self.cleanup_target)
        space_to_free = (self._current_size + required_bytes) - target_size
        
        if space_to_free <= 0:
            return True
        
        # Evict entries based on policy
        return self._evict_entries(space_to_free)
    
    def _evict_entries(self, space_to_free: int) -> bool:
        """Evict entries to free space.
        
        Args:
            space_to_free: Amount of space to free in bytes
            
        Returns:
            True if sufficient space was freed
        """
        freed_space = 0
        evicted_keys = []
        
        # Get eviction candidates based on policy
        candidates = self._get_eviction_candidates()
        
        for key in candidates:
            entry = self._cache.get(key)
            if entry is None:
                continue
            
            # Skip pinned entries
            if entry.pinned:
                continue
            
            # Skip critical priority entries unless under extreme pressure
            if entry.priority == MemoryPriority.CRITICAL:
                usage_ratio = self._current_size / self.max_size_bytes
                if usage_ratio < 0.95:  # Only evict critical under extreme pressure
                    continue
            
            # Evict entry
            freed_space += entry.size_bytes
            evicted_keys.append(key)
            
            if freed_space >= space_to_free:
                break
        
        # Remove evicted entries
        for key in evicted_keys:
            self._remove_entry(key)
            self._evictions += 1
        
        if freed_space > 0:
            self.logger.debug(f"Evicted {len(evicted_keys)} entries, freed {freed_space / 1024**2:.1f} MB")
        
        return freed_space >= space_to_free
    
    def _get_eviction_candidates(self) -> List[str]:
        """Get eviction candidates based on policy.
        
        Returns:
            List of keys ordered by eviction priority
        """
        if self.policy == CachePolicy.LRU:
            return list(self._access_order.keys())
        
        elif self.policy == CachePolicy.LFU:
            # Sort by frequency (ascending) then by last access (ascending)
            items = []
            for key, entry in self._cache.items():
                frequency = self._frequency_counter.get(key, 0)
                items.append((frequency, entry.last_access, key))
            
            items.sort(key=lambda x: (x[0], x[1]))
            return [item[2] for item in items]
        
        elif self.policy == CachePolicy.FIFO:
            # Sort by creation time
            items = [(entry.created_at, key) for key, entry in self._cache.items()]
            items.sort()
            return [item[1] for item in items]
        
        elif self.policy == CachePolicy.TTL:
            # Sort by expiration time (expired first, then by remaining TTL)
            items = []
            now = datetime.now()
            
            for key, entry in self._cache.items():
                if entry.ttl_seconds is None:
                    # No TTL, use creation time
                    priority = entry.created_at.timestamp()
                else:
                    expires_at = entry.created_at + timedelta(seconds=entry.ttl_seconds)
                    if expires_at <= now:
                        # Expired, highest priority for eviction
                        priority = -1
                    else:
                        # Sort by remaining TTL
                        priority = (expires_at - now).total_seconds()
                
                items.append((priority, key))
            
            items.sort()
            return [item[1] for item in items]
        
        elif self.policy == CachePolicy.SIZE_BASED:
            # Sort by size (largest first) and priority (low priority first)
            items = []
            for key, entry in self._cache.items():
                priority_weight = {
                    MemoryPriority.LOW: 0,
                    MemoryPriority.MEDIUM: 1,
                    MemoryPriority.HIGH: 2,
                    MemoryPriority.CRITICAL: 3
                }[entry.priority]
                
                # Sort by priority (ascending) then by size (descending)
                items.append((priority_weight, -entry.size_bytes, key))
            
            items.sort()
            return [item[2] for item in items]
        
        else:
            # Default to LRU
            return list(self._cache.keys())
    
    def _remove_entry(self, key: str) -> bool:
        """Remove entry from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if entry was removed
        """
        entry = self._cache.pop(key, None)
        if entry is None:
            return False
        
        self._current_size -= entry.size_bytes
        
        # Clean up policy-specific data structures
        self._access_order.pop(key, None)
        self._frequency_counter.pop(key, None)
        
        return True
    
    def _maybe_cleanup(self) -> None:
        """Perform periodic cleanup if needed."""
        now = datetime.now()
        if now - self._last_cleanup < self._cleanup_interval:
            return
        
        self._last_cleanup = now
        
        # Remove expired entries
        expired_keys = []
        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_entry(key)
        
        if expired_keys:
            self.logger.debug(f"Cleaned up {len(expired_keys)} expired entries")
        
        # Check if cleanup is needed
        usage_ratio = self._current_size / self.max_size_bytes
        if usage_ratio > self.cleanup_threshold:
            target_size = int(self.max_size_bytes * self.cleanup_target)
            space_to_free = self._current_size - target_size
            
            if space_to_free > 0:
                self._evict_entries(space_to_free)


class MemoryMappedStorage:
    """Memory-mapped file storage for large datasets."""
    
    def __init__(self, base_path: Optional[str] = None):
        """Initialize memory-mapped storage.
        
        Args:
            base_path: Base path for storage files
        """
        self.base_path = Path(base_path) if base_path else Path(tempfile.gettempdir()) / "backtester_mmap"
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_logger("mmap_storage")
        
        # Active mappings
        self._mappings: Dict[str, Tuple[mmap.mmap, Any]] = {}
        self._files: Dict[str, Any] = {}
        
        # Threading
        self._lock = Lock()
    
    def store_dataframe(self, key: str, df: pd.DataFrame) -> str:
        """Store DataFrame using memory mapping.
        
        Args:
            key: Storage key
            df: DataFrame to store
            
        Returns:
            File path
        """
        file_path = self.base_path / f"{key}.parquet"
        
        with self._lock:
            # Save DataFrame
            df.to_parquet(file_path, compression='snappy')
            
            self.logger.debug(f"Stored DataFrame to {file_path} ({len(df)} rows)")
            
            return str(file_path)
    
    def load_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """Load DataFrame from memory mapping.
        
        Args:
            key: Storage key
            
        Returns:
            DataFrame or None if not found
        """
        file_path = self.base_path / f"{key}.parquet"
        
        if not file_path.exists():
            return None
        
        try:
            df = pd.read_parquet(file_path)
            self.logger.debug(f"Loaded DataFrame from {file_path} ({len(df)} rows)")
            return df
        
        except Exception as e:
            self.logger.error(f"Failed to load DataFrame from {file_path}: {e}")
            return None
    
    def store_array(self, key: str, array: np.ndarray) -> str:
        """Store NumPy array using memory mapping.
        
        Args:
            key: Storage key
            array: Array to store
            
        Returns:
            File path
        """
        file_path = self.base_path / f"{key}.npy"
        
        with self._lock:
            # Save array
            np.save(file_path, array)
            
            self.logger.debug(f"Stored array to {file_path} ({array.shape})")
            
            return str(file_path)
    
    def load_array(self, key: str, mmap_mode: str = 'r') -> Optional[np.ndarray]:
        """Load NumPy array with memory mapping.
        
        Args:
            key: Storage key
            mmap_mode: Memory mapping mode ('r', 'r+', 'w+', 'c')
            
        Returns:
            Array or None if not found
        """
        file_path = self.base_path / f"{key}.npy"
        
        if not file_path.exists():
            return None
        
        try:
            array = np.load(file_path, mmap_mode=mmap_mode)
            self.logger.debug(f"Loaded array from {file_path} ({array.shape})")
            return array
        
        except Exception as e:
            self.logger.error(f"Failed to load array from {file_path}: {e}")
            return None
    
    def remove(self, key: str) -> bool:
        """Remove stored data.
        
        Args:
            key: Storage key
            
        Returns:
            True if removed
        """
        removed = False
        
        # Try different file extensions
        for ext in ['.parquet', '.npy', '.pkl']:
            file_path = self.base_path / f"{key}{ext}"
            if file_path.exists():
                try:
                    file_path.unlink()
                    removed = True
                    self.logger.debug(f"Removed {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to remove {file_path}: {e}")
        
        return removed
    
    def cleanup(self) -> None:
        """Clean up storage directory."""
        try:
            shutil.rmtree(self.base_path)
            self.base_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Cleaned up storage directory: {self.base_path}")
        except Exception as e:
            self.logger.error(f"Failed to cleanup storage: {e}")
    
    def get_size(self) -> int:
        """Get total storage size in bytes."""
        total_size = 0
        
        try:
            for file_path in self.base_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception as e:
            self.logger.error(f"Failed to calculate storage size: {e}")
        
        return total_size


class MemoryManager:
    """Main memory management coordinator."""
    
    def __init__(
        self,
        cache_size_gb: float = 8.0,
        cache_policy: CachePolicy = CachePolicy.LRU,
        enable_monitoring: bool = True,
        storage_path: Optional[str] = None
    ):
        """Initialize memory manager.
        
        Args:
            cache_size_gb: Cache size in GB
            cache_policy: Cache eviction policy
            enable_monitoring: Whether to enable memory monitoring
            storage_path: Path for memory-mapped storage
        """
        self.logger = get_logger("memory_manager")
        
        # Components
        self.cache = CacheManager(cache_size_gb, cache_policy)
        self.monitor = MemoryMonitor()
        self.storage = MemoryMappedStorage(storage_path)
        
        # Configuration
        self.enable_monitoring = enable_monitoring
        
        # Register callbacks
        self.monitor.add_warning_callback(self._on_memory_warning)
        self.monitor.add_critical_callback(self._on_memory_critical)
        
        # Start monitoring
        if enable_monitoring:
            self.monitor.start_monitoring()
        
        self.logger.info("Memory manager initialized")
    
    def get_or_compute(
        self,
        key: str,
        compute_func: Callable[[], Any],
        priority: MemoryPriority = MemoryPriority.MEDIUM,
        ttl_seconds: Optional[int] = None,
        use_storage: bool = False
    ) -> Any:
        """Get data from cache or compute if not available.
        
        Args:
            key: Cache key
            compute_func: Function to compute data if not cached
            priority: Memory priority
            ttl_seconds: Time to live in seconds
            use_storage: Whether to use persistent storage
            
        Returns:
            Cached or computed data
        """
        # Try cache first
        data = self.cache.get(key)
        if data is not None:
            return data
        
        # Try storage if enabled
        if use_storage:
            if key.endswith('_df'):
                data = self.storage.load_dataframe(key)
            elif key.endswith('_array'):
                data = self.storage.load_array(key)
            
            if data is not None:
                # Put back in cache
                self.cache.put(key, data, priority, ttl_seconds)
                return data
        
        # Compute data
        data = compute_func()
        
        # Store in cache
        self.cache.put(key, data, priority, ttl_seconds)
        
        # Store persistently if enabled
        if use_storage:
            if isinstance(data, pd.DataFrame):
                self.storage.store_dataframe(key, data)
            elif isinstance(data, np.ndarray):
                self.storage.store_array(key, data)
        
        return data
    
    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.cache.clear()
        self.logger.info("Cache cleared")
    
    def clear_storage(self) -> None:
        """Clear all stored data."""
        self.storage.cleanup()
        self.logger.info("Storage cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics."""
        cache_stats = self.cache.get_stats()
        memory_stats = self.monitor.get_memory_stats(self.cache)
        storage_size = self.storage.get_size()
        
        return {
            'cache': cache_stats,
            'memory': {
                'total_gb': memory_stats.total_memory_gb,
                'available_gb': memory_stats.available_memory_gb,
                'used_gb': memory_stats.used_memory_gb,
                'pressure': memory_stats.memory_pressure
            },
            'storage': {
                'size_bytes': storage_size,
                'size_mb': storage_size / 1024**2
            }
        }
    
    def optimize_memory(self) -> None:
        """Perform memory optimization."""
        self.logger.info("Starting memory optimization")
        
        # Force cache cleanup
        self.cache._maybe_cleanup()
        
        # Force garbage collection
        collected = gc.collect()
        
        self.logger.info(f"Memory optimization completed, collected {collected} objects")
    
    def _on_memory_warning(self, stats: MemoryStats) -> None:
        """Handle memory warning."""
        self.logger.warning(
            f"Memory warning: {stats.memory_pressure:.1%} usage, "
            f"{stats.available_memory_gb:.1f}GB available"
        )
        
        # Trigger cache cleanup
        self.cache._maybe_cleanup()
    
    def _on_memory_critical(self, stats: MemoryStats) -> None:
        """Handle critical memory situation."""
        self.logger.error(
            f"Critical memory situation: {stats.memory_pressure:.1%} usage, "
            f"{stats.available_memory_gb:.1f}GB available"
        )
        
        # Aggressive cleanup
        self.cache.clear()
        gc.collect()
    
    def shutdown(self) -> None:
        """Shutdown memory manager."""
        if self.enable_monitoring:
            self.monitor.stop_monitoring()
        
        self.logger.info("Memory manager shutdown")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


# Global memory manager instance
_global_memory_manager: Optional[MemoryManager] = None


def get_memory_manager() -> MemoryManager:
    """Get global memory manager instance."""
    global _global_memory_manager
    
    if _global_memory_manager is None:
        config = get_config()
        
        _global_memory_manager = MemoryManager(
            cache_size_gb=getattr(config, 'cache_size_gb', 8.0),
            cache_policy=CachePolicy(getattr(config, 'cache_policy', 'lru')),
            enable_monitoring=getattr(config, 'enable_memory_monitoring', True)
        )
    
    return _global_memory_manager


def clear_global_cache() -> None:
    """Clear global cache."""
    manager = get_memory_manager()
    manager.clear_cache()


def get_memory_stats() -> Dict[str, Any]:
    """Get global memory statistics."""
    manager = get_memory_manager()
    return manager.get_stats()