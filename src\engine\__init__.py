"""Core backtesting engine module for cryptocurrency trading strategies.

This module provides the main backtesting engine built on Backtrader with
optimizations for cryptocurrency data and parallel processing capabilities.

Classes:
    BacktestEngine: Main backtesting engine
    PortfolioManager: Portfolio and position management
    RiskManager: Risk management and position sizing
    ExecutionEngine: Order execution and slippage modeling
    PerformanceTracker: Real-time performance tracking
    EventManager: Event-driven architecture coordinator

Functions:
    run_backtest: Convenience function for running backtests
    create_engine: Factory function for engine creation
    validate_backtest: Validate backtest configuration

Example:
    >>> from src.engine import BacktestEngine, PortfolioManager
    >>> from src.strategies import MovingAverageCrossover
    >>> 
    >>> # Create engine with portfolio manager
    >>> portfolio = PortfolioManager(initial_cash=100000)
    >>> engine = BacktestEngine(portfolio=portfolio)
    >>> 
    >>> # Add strategy
    >>> strategy = MovingAverageCrossover(fast_period=20, slow_period=50)
    >>> engine.add_strategy(strategy)
    >>> 
    >>> # Run backtest
    >>> results = engine.run(data=market_data)
    >>> 
    >>> # Get performance metrics
    >>> metrics = results.get_performance_metrics()
    >>> print(f"Total Return: {metrics['total_return']:.2%}")
    >>> print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.4f}")
"""

# Import core engine components
try:
    from .backtest_engine import BacktestEngine
    from .portfolio_manager import PortfolioManager
    from .risk_manager import RiskManager
    from .execution_engine import ExecutionEngine
    from .performance_tracker import PerformanceTracker
    from .event_manager import EventManager
    from .result import BacktestResult
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Engine configuration defaults
DEFAULT_ENGINE_CONFIG = {
    'initial_cash': 100000,
    'commission': 0.001,        # 0.1% commission
    'slippage': 0.0005,         # 0.05% slippage
    'margin': 1.0,              # No leverage by default
    'risk_free_rate': 0.02,     # 2% risk-free rate
    'benchmark': 'BTC/USDT',    # Default benchmark
    'timezone': 'UTC',
    'currency': 'USDT'
}

# Risk management defaults
DEFAULT_RISK_CONFIG = {
    'max_position_size': 0.1,   # 10% max position size
    'max_portfolio_risk': 0.02, # 2% max portfolio risk per trade
    'stop_loss': 0.05,          # 5% stop loss
    'take_profit': 0.15,        # 15% take profit
    'max_drawdown': 0.2,        # 20% max drawdown
    'var_confidence': 0.95,     # 95% VaR confidence
    'correlation_limit': 0.7    # Max correlation between positions
}

# Performance tracking settings
PERFORMACE_METRICS = [
    'total_return',
    'annual_return', 
    'sharpe_ratio',
    'sortino_ratio',
    'calmar_ratio',
    'max_drawdown',
    'win_rate',
    'profit_factor',
    'avg_trade_return',
    'total_trades',
    'volatility',
    'skewness',
    'kurtosis',
    'var_95',
    'cvar_95'
]

# Event types for event-driven architecture
EVENT_TYPES = {
    'MARKET_DATA': 'market_data',
    'SIGNAL': 'signal',
    'ORDER': 'order',
    'FILL': 'fill',
    'PORTFOLIO_UPDATE': 'portfolio_update',
    'RISK_CHECK': 'risk_check',
    'PERFORMANCE_UPDATE': 'performance_update'
}

# Order types
ORDER_TYPES = {
    'MARKET': 'market',
    'LIMIT': 'limit',
    'STOP': 'stop',
    'STOP_LIMIT': 'stop_limit'
}

# Position sizing methods
POSITION_SIZING_METHODS = {
    'FIXED': 'fixed',
    'PERCENT_RISK': 'percent_risk',
    'KELLY': 'kelly',
    'VOLATILITY_TARGET': 'volatility_target',
    'EQUAL_WEIGHT': 'equal_weight'
}

def run_backtest(strategy, data, config=None, **kwargs):
    """Convenience function for running a backtest.
    
    Args:
        strategy: Trading strategy instance
        data: Market data for backtesting
        config: Engine configuration dictionary
        **kwargs: Additional backtest parameters
        
    Returns:
        BacktestResult: Backtest results and performance metrics
    """
    # This would be implemented when the actual engine classes are created
    raise NotImplementedError("run_backtest will be implemented with engine classes")

def create_engine(config=None, **kwargs):
    """Factory function for creating a backtest engine.
    
    Args:
        config: Engine configuration dictionary
        **kwargs: Additional engine parameters
        
    Returns:
        BacktestEngine: Configured backtest engine
    """
    # This would be implemented when the actual engine classes are created
    raise NotImplementedError("create_engine will be implemented with engine classes")

def validate_backtest(strategy, data, config):
    """Validate backtest configuration before execution.
    
    Args:
        strategy: Trading strategy instance
        data: Market data for backtesting
        config: Engine configuration dictionary
        
    Returns:
        bool: True if configuration is valid
        
    Raises:
        ValueError: If configuration is invalid
    """
    # This would be implemented when the actual validation logic is created
    raise NotImplementedError("validate_backtest will be implemented with validation logic")

# Export public API
__all__ = [
    "BacktestEngine",
    "PortfolioManager",
    "RiskManager",
    "ExecutionEngine",
    "PerformanceTracker",
    "EventManager",
    "BacktestResult",
    "run_backtest",
    "create_engine",
    "validate_backtest",
    "DEFAULT_ENGINE_CONFIG",
    "DEFAULT_RISK_CONFIG",
    "PERFORMANCE_METRICS",
    "EVENT_TYPES",
    "ORDER_TYPES",
    "POSITION_SIZING_METHODS",
]