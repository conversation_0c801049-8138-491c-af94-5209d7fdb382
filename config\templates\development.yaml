# Development Configuration for Crypto Backtesting System
# This configuration is optimized for development and testing

# Application settings
app:
  name: "crypto-backtester"
  version: "0.1.0"
  environment: "development"
  debug: true
  timezone: "UTC"

# Logging configuration
logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/development.log"
  max_size: "100MB"
  backup_count: 5
  console: true
  colored: true
  
  # Module-specific logging levels
  loggers:
    backtrader: "INFO"
    ccxt: "WARNING"
    urllib3: "WARNING"
    requests: "WARNING"

# Hardware optimization (Ryzen 9 7950X + 96GB DDR5)
hardware:
  cpu:
    cores: 16
    threads: 32
    workers: 16  # Conservative for development
    affinity: null  # Let OS handle
    
  memory:
    total_gb: 96
    available_gb: 76  # Reserve 20GB for OS
    chunk_size_mb: 512
    cache_size_gb: 8
    gc_threshold: 0.8
    
  gpu:
    enabled: true
    device: "cuda:0"  # RTX 5060Ti
    memory_gb: 16
    compute_capability: "8.9"

# Parallel processing
parallel:
  enabled: true
  max_workers: 16
  chunk_size: 1000
  timeout: 3600
  memory_limit_gb: 4  # Per worker
  
  # Process pool settings
  pool:
    type: "process"  # or "thread"
    initializer: null
    maxtasksperchild: 100
    
  # Task scheduling
  scheduler:
    strategy: "round_robin"  # or "load_balanced"
    queue_size: 1000
    batch_size: 10

# Data management
data:
  # Storage paths
  paths:
    raw: "data/raw"
    processed: "data/processed"
    cache: "data/cache"
    results: "data/results"
    
  # Data sources
  exchanges:
    binance:
      enabled: true
      api_key: null  # Set via environment variable
      api_secret: null  # Set via environment variable
      sandbox: true
      rate_limit: 1200  # requests per minute
      
    coinbase:
      enabled: false
      api_key: null
      api_secret: null
      sandbox: true
      
  # Data quality
  quality:
    min_data_points: 100
    max_missing_ratio: 0.05
    outlier_threshold: 3.0
    validate_ohlcv: true
    
  # Caching
  cache:
    enabled: true
    backend: "hdf5"  # or "redis", "sqlite"
    ttl: 86400  # 24 hours
    compression: "blosc"
    
# Backtesting engine
engine:
  # Default settings
  initial_cash: 100000
  commission: 0.001
  slippage: 0.0001
  
  # Risk management
  risk:
    max_position_size: 0.1  # 10% of portfolio
    max_drawdown: 0.2  # 20%
    stop_loss: 0.05  # 5%
    take_profit: 0.15  # 15%
    
  # Performance tracking
  metrics:
    calculate_all: true
    benchmark: "BTC/USDT"
    risk_free_rate: 0.02
    
# Strategy configuration
strategies:
  # Default parameters
  defaults:
    timeframe: "1h"
    lookback: 100
    min_periods: 20
    
  # Strategy registry
  registry:
    ma_crossover:
      class: "MovingAverageCrossover"
      params:
        fast_period: 10
        slow_period: 30
        
    rsi:
      class: "RSIStrategy"
      params:
        period: 14
        oversold: 30
        overbought: 70
        
    bollinger_bands:
      class: "BollingerBandsStrategy"
      params:
        period: 20
        std_dev: 2.0
        
# Optimization settings
optimization:
  # Algorithm settings
  algorithms:
    grid_search:
      enabled: true
      max_combinations: 10000
      
    bayesian:
      enabled: true
      n_calls: 100
      acq_func: "EI"  # Expected Improvement
      
    genetic:
      enabled: true
      population_size: 50
      generations: 100
      mutation_rate: 0.1
      
  # Objectives
  objectives:
    primary: "sharpe_ratio"
    secondary: "total_return"
    constraints:
      min_trades: 10
      max_drawdown: 0.3
      
  # Performance thresholds
  thresholds:
    min_sharpe: 1.0
    min_win_rate: 0.4
    max_drawdown: 0.2
    
# Monitoring and alerting
monitoring:
  enabled: true
  interval: 30  # seconds
  
  # System metrics
  metrics:
    cpu: true
    memory: true
    disk: true
    gpu: true
    network: false
    
  # Alerts
  alerts:
    cpu_threshold: 0.9
    memory_threshold: 0.9
    disk_threshold: 0.9
    
  # Dashboard
  dashboard:
    enabled: true
    host: "localhost"
    port: 8080
    auto_refresh: 5  # seconds
    
# Database configuration
database:
  # Results storage
  results:
    type: "sqlite"
    path: "data/results.db"
    
  # Cache storage
  cache:
    type: "redis"
    host: "localhost"
    port: 6379
    db: 0
    
# Security settings
security:
  # API keys (use environment variables)
  env_vars:
    - "BINANCE_API_KEY"
    - "BINANCE_API_SECRET"
    - "COINBASE_API_KEY"
    - "COINBASE_API_SECRET"
    
  # Encryption
  encryption:
    enabled: false
    algorithm: "AES-256"
    
# Development tools
development:
  # Testing
  testing:
    data_dir: "tests/data"
    fixtures_dir: "tests/fixtures"
    coverage_threshold: 0.8
    
  # Profiling
  profiling:
    enabled: true
    memory: true
    cpu: true
    output_dir: "logs/profiling"
    
  # Hot reload
  hot_reload:
    enabled: true
    watch_dirs:
      - "src"
      - "config"
      
# Feature flags
features:
  gpu_acceleration: true
  parallel_optimization: true
  real_time_monitoring: true
  advanced_metrics: true
  machine_learning: false  # Disabled in development
  
# External services
services:
  # Data providers
  data_providers:
    alpha_vantage:
      enabled: false
      api_key: null
      
    quandl:
      enabled: false
      api_key: null
      
  # Notification services
  notifications:
    email:
      enabled: false
      smtp_server: null
      
    slack:
      enabled: false
      webhook_url: null