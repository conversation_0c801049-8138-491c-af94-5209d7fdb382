"""Performance analysis module for cryptocurrency backtesting system.

This module provides comprehensive analysis of backtesting results including
performance metrics, risk analysis, drawdown analysis, and reporting capabilities.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import warnings
from pathlib import Path
import json

# Statistical and financial libraries
import scipy.stats as stats
from scipy.optimize import minimize
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller

# Plotting libraries
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg

from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class AnalysisType(Enum):
    """Types of analysis."""
    RETURNS = "returns"
    RISK = "risk"
    DRAWDOWN = "drawdown"
    TRADES = "trades"
    PORTFOLIO = "portfolio"
    BENCHMARK = "benchmark"
    ATTRIBUTION = "attribution"


class RiskMetric(Enum):
    """Risk metrics."""
    SHARPE_RATIO = "sharpe_ratio"
    SORTINO_RATIO = "sortino_ratio"
    CALMAR_RATIO = "calmar_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    VAR = "value_at_risk"
    CVAR = "conditional_var"
    BETA = "beta"
    ALPHA = "alpha"
    VOLATILITY = "volatility"
    SKEWNESS = "skewness"
    KURTOSIS = "kurtosis"


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    # Return metrics
    total_return: float
    annualized_return: float
    cumulative_return: float
    
    # Risk metrics
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Drawdown metrics
    max_drawdown: float
    max_drawdown_duration: int
    avg_drawdown: float
    drawdown_recovery_time: float
    
    # Risk measures
    value_at_risk_95: float
    value_at_risk_99: float
    conditional_var_95: float
    conditional_var_99: float
    
    # Distribution metrics
    skewness: float
    kurtosis: float
    
    # Benchmark comparison
    beta: Optional[float] = None
    alpha: Optional[float] = None
    correlation: Optional[float] = None
    tracking_error: Optional[float] = None
    information_ratio: Optional[float] = None
    
    # Trade metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Time-based metrics
    analysis_start: Optional[datetime] = None
    analysis_end: Optional[datetime] = None
    analysis_period_days: int = 0


@dataclass
class DrawdownAnalysis:
    """Detailed drawdown analysis."""
    drawdown_series: pd.Series
    max_drawdown: float
    max_drawdown_start: datetime
    max_drawdown_end: datetime
    max_drawdown_duration: int
    recovery_date: Optional[datetime]
    
    # Drawdown statistics
    avg_drawdown: float
    median_drawdown: float
    drawdown_frequency: float
    avg_recovery_time: float
    
    # Drawdown periods
    drawdown_periods: List[Dict[str, Any]]
    
    # Underwater curve
    underwater_curve: pd.Series


@dataclass
class TradeAnalysis:
    """Detailed trade analysis."""
    # Basic statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    
    # Return statistics
    avg_return: float
    avg_win: float
    avg_loss: float
    best_trade: float
    worst_trade: float
    
    # Risk metrics
    profit_factor: float
    expectancy: float
    
    # Duration analysis
    avg_trade_duration: float
    avg_winning_duration: float
    avg_losing_duration: float
    
    # Consecutive trades
    max_consecutive_wins: int
    max_consecutive_losses: int
    
    # Monthly analysis
    monthly_returns: pd.Series
    monthly_win_rate: pd.Series
    
    # Trade distribution
    return_distribution: pd.Series
    duration_distribution: pd.Series


@dataclass
class RiskAnalysis:
    """Comprehensive risk analysis."""
    # Volatility measures
    daily_volatility: float
    monthly_volatility: float
    annualized_volatility: float
    
    # Value at Risk
    var_95: float
    var_99: float
    var_99_9: float
    
    # Conditional VaR (Expected Shortfall)
    cvar_95: float
    cvar_99: float
    cvar_99_9: float
    
    # Distribution analysis
    skewness: float
    kurtosis: float
    jarque_bera_stat: float
    jarque_bera_pvalue: float
    is_normal: bool
    
    # Tail risk
    tail_ratio: float
    
    # Stress testing
    stress_scenarios: Dict[str, float]
    
    # Risk attribution
    risk_contribution: Dict[str, float]


class PerformanceAnalyzer:
    """Main performance analysis engine."""
    
    def __init__(self, risk_free_rate: float = 0.02):
        """Initialize performance analyzer.
        
        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate
        self.logger = get_logger("performance_analyzer")
        
        # Analysis cache
        self._analysis_cache: Dict[str, Any] = {}
        
        # Configuration
        self.config = get_config()
    
    def analyze_returns(
        self,
        returns: pd.Series,
        benchmark_returns: Optional[pd.Series] = None,
        trades: Optional[pd.DataFrame] = None
    ) -> PerformanceMetrics:
        """Comprehensive performance analysis.
        
        Args:
            returns: Daily returns series
            benchmark_returns: Optional benchmark returns
            trades: Optional trades DataFrame
            
        Returns:
            Performance metrics
        """
        self.logger.info("Starting performance analysis")
        
        # Validate inputs
        returns = self._validate_returns(returns)
        
        # Calculate basic metrics
        metrics = self._calculate_basic_metrics(returns)
        
        # Add risk metrics
        risk_metrics = self._calculate_risk_metrics(returns)
        for key, value in risk_metrics.items():
            setattr(metrics, key, value)
        
        # Add drawdown metrics
        drawdown_metrics = self._calculate_drawdown_metrics(returns)
        for key, value in drawdown_metrics.items():
            setattr(metrics, key, value)
        
        # Benchmark comparison if provided
        if benchmark_returns is not None:
            benchmark_metrics = self._calculate_benchmark_metrics(returns, benchmark_returns)
            for key, value in benchmark_metrics.items():
                setattr(metrics, key, value)
        
        # Trade analysis if provided
        if trades is not None:
            trade_metrics = self._calculate_trade_metrics(trades)
            for key, value in trade_metrics.items():
                setattr(metrics, key, value)
        
        # Set analysis period
        metrics.analysis_start = returns.index[0]
        metrics.analysis_end = returns.index[-1]
        metrics.analysis_period_days = len(returns)
        
        self.logger.info(f"Performance analysis completed for {len(returns)} periods")
        return metrics
    
    def _validate_returns(self, returns: pd.Series) -> pd.Series:
        """Validate and clean returns series."""
        if not isinstance(returns, pd.Series):
            raise ValueError("Returns must be a pandas Series")
        
        if len(returns) == 0:
            raise ValueError("Returns series is empty")
        
        # Remove NaN values
        returns = returns.dropna()
        
        # Check for infinite values
        if np.isinf(returns).any():
            self.logger.warning("Infinite values found in returns, replacing with NaN")
            returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
        
        # Sort by index
        returns = returns.sort_index()
        
        return returns
    
    def _calculate_basic_metrics(self, returns: pd.Series) -> PerformanceMetrics:
        """Calculate basic performance metrics."""
        # Calculate cumulative returns
        cumulative_returns = (1 + returns).cumprod()
        
        # Total return
        total_return = cumulative_returns.iloc[-1] - 1
        
        # Annualized return
        periods_per_year = self._get_periods_per_year(returns)
        annualized_return = (1 + total_return) ** (periods_per_year / len(returns)) - 1
        
        # Volatility
        volatility = returns.std() * np.sqrt(periods_per_year)
        
        # Sharpe ratio
        excess_returns = returns - self.risk_free_rate / periods_per_year
        sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(periods_per_year) if returns.std() > 0 else 0
        
        # Sortino ratio (using downside deviation)
        downside_returns = returns[returns < 0]
        downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
        sortino_ratio = excess_returns.mean() / downside_std * np.sqrt(periods_per_year) if downside_std > 0 else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            cumulative_return=total_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=0.0,  # Will be calculated in drawdown metrics
            max_drawdown=0.0,
            max_drawdown_duration=0,
            avg_drawdown=0.0,
            drawdown_recovery_time=0.0,
            value_at_risk_95=0.0,
            value_at_risk_99=0.0,
            conditional_var_95=0.0,
            conditional_var_99=0.0,
            skewness=0.0,
            kurtosis=0.0
        )
    
    def _calculate_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate risk metrics."""
        # Value at Risk
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        
        # Conditional VaR (Expected Shortfall)
        cvar_95 = returns[returns <= var_95].mean() if len(returns[returns <= var_95]) > 0 else var_95
        cvar_99 = returns[returns <= var_99].mean() if len(returns[returns <= var_99]) > 0 else var_99
        
        # Distribution metrics
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        return {
            'value_at_risk_95': var_95,
            'value_at_risk_99': var_99,
            'conditional_var_95': cvar_95,
            'conditional_var_99': cvar_99,
            'skewness': skewness,
            'kurtosis': kurtosis
        }
    
    def _calculate_drawdown_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate drawdown metrics."""
        # Calculate cumulative returns
        cumulative_returns = (1 + returns).cumprod()
        
        # Calculate running maximum
        running_max = cumulative_returns.expanding().max()
        
        # Calculate drawdown
        drawdown = (cumulative_returns - running_max) / running_max
        
        # Maximum drawdown
        max_drawdown = drawdown.min()
        
        # Maximum drawdown duration
        max_dd_duration = self._calculate_max_drawdown_duration(drawdown)
        
        # Average drawdown
        avg_drawdown = drawdown[drawdown < 0].mean() if len(drawdown[drawdown < 0]) > 0 else 0
        
        # Recovery time
        recovery_time = self._calculate_recovery_time(drawdown)
        
        # Calmar ratio
        periods_per_year = self._get_periods_per_year(returns)
        annualized_return = (1 + (cumulative_returns.iloc[-1] - 1)) ** (periods_per_year / len(returns)) - 1
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_duration': max_dd_duration,
            'avg_drawdown': avg_drawdown,
            'drawdown_recovery_time': recovery_time,
            'calmar_ratio': calmar_ratio
        }
    
    def _calculate_benchmark_metrics(self, returns: pd.Series, benchmark_returns: pd.Series) -> Dict[str, float]:
        """Calculate benchmark comparison metrics."""
        # Align series
        aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
        
        if len(aligned_returns) == 0:
            return {}
        
        # Beta and Alpha (using CAPM)
        covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
        benchmark_variance = np.var(aligned_benchmark)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
        
        # Alpha
        periods_per_year = self._get_periods_per_year(returns)
        portfolio_return = aligned_returns.mean() * periods_per_year
        benchmark_return = aligned_benchmark.mean() * periods_per_year
        alpha = portfolio_return - (self.risk_free_rate + beta * (benchmark_return - self.risk_free_rate))
        
        # Correlation
        correlation = aligned_returns.corr(aligned_benchmark)
        
        # Tracking error
        tracking_error = (aligned_returns - aligned_benchmark).std() * np.sqrt(periods_per_year)
        
        # Information ratio
        excess_return = (aligned_returns - aligned_benchmark).mean() * periods_per_year
        information_ratio = excess_return / tracking_error if tracking_error > 0 else 0
        
        return {
            'beta': beta,
            'alpha': alpha,
            'correlation': correlation,
            'tracking_error': tracking_error,
            'information_ratio': information_ratio
        }
    
    def _calculate_trade_metrics(self, trades: pd.DataFrame) -> Dict[str, Any]:
        """Calculate trade-based metrics."""
        if trades.empty:
            return {}
        
        # Basic trade statistics
        total_trades = len(trades)
        winning_trades = len(trades[trades['pnl'] > 0])
        losing_trades = len(trades[trades['pnl'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Return statistics
        avg_win = trades[trades['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades[trades['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # Profit factor
        total_wins = trades[trades['pnl'] > 0]['pnl'].sum()
        total_losses = abs(trades[trades['pnl'] < 0]['pnl'].sum())
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor
        }
    
    def _calculate_max_drawdown_duration(self, drawdown: pd.Series) -> int:
        """Calculate maximum drawdown duration in periods."""
        # Find periods where we're in drawdown
        in_drawdown = drawdown < 0
        
        if not in_drawdown.any():
            return 0
        
        # Find consecutive drawdown periods
        drawdown_periods = []
        current_period = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_period += 1
            else:
                if current_period > 0:
                    drawdown_periods.append(current_period)
                current_period = 0
        
        # Add final period if we end in drawdown
        if current_period > 0:
            drawdown_periods.append(current_period)
        
        return max(drawdown_periods) if drawdown_periods else 0
    
    def _calculate_recovery_time(self, drawdown: pd.Series) -> float:
        """Calculate average recovery time from drawdowns."""
        # Find drawdown periods and their recovery times
        recovery_times = []
        in_drawdown = False
        drawdown_start = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                drawdown_start = i
            elif dd >= 0 and in_drawdown:
                # End of drawdown (recovery)
                in_drawdown = False
                if drawdown_start is not None:
                    recovery_times.append(i - drawdown_start)
        
        return np.mean(recovery_times) if recovery_times else 0
    
    def _get_periods_per_year(self, returns: pd.Series) -> int:
        """Determine periods per year based on data frequency."""
        if len(returns) < 2:
            return 252  # Default to daily
        
        # Calculate average time delta
        time_deltas = returns.index[1:] - returns.index[:-1]
        avg_delta = time_deltas.mean()
        
        # Determine frequency
        if avg_delta <= timedelta(days=1):
            return 252  # Daily
        elif avg_delta <= timedelta(days=7):
            return 52   # Weekly
        elif avg_delta <= timedelta(days=31):
            return 12   # Monthly
        else:
            return 4    # Quarterly
    
    def analyze_drawdowns(self, returns: pd.Series) -> DrawdownAnalysis:
        """Detailed drawdown analysis.
        
        Args:
            returns: Daily returns series
            
        Returns:
            Drawdown analysis
        """
        returns = self._validate_returns(returns)
        
        # Calculate cumulative returns and drawdowns
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        
        # Find maximum drawdown
        max_dd_idx = drawdown.idxmin()
        max_drawdown = drawdown.min()
        
        # Find start of maximum drawdown period
        max_dd_start_idx = running_max.loc[:max_dd_idx].idxmax()
        
        # Find recovery date
        recovery_idx = None
        peak_value = running_max.loc[max_dd_start_idx]
        
        for idx in cumulative_returns.loc[max_dd_idx:].index:
            if cumulative_returns.loc[idx] >= peak_value:
                recovery_idx = idx
                break
        
        # Calculate duration
        max_dd_duration = (max_dd_idx - max_dd_start_idx).days
        
        # Analyze all drawdown periods
        drawdown_periods = self._identify_drawdown_periods(drawdown, cumulative_returns)
        
        # Calculate statistics
        avg_drawdown = drawdown[drawdown < 0].mean() if len(drawdown[drawdown < 0]) > 0 else 0
        median_drawdown = drawdown[drawdown < 0].median() if len(drawdown[drawdown < 0]) > 0 else 0
        
        # Drawdown frequency (periods in drawdown / total periods)
        drawdown_frequency = len(drawdown[drawdown < 0]) / len(drawdown)
        
        # Average recovery time
        recovery_times = [period['recovery_time'] for period in drawdown_periods if period['recovery_time'] is not None]
        avg_recovery_time = np.mean(recovery_times) if recovery_times else 0
        
        # Underwater curve (time below previous peak)
        underwater_curve = drawdown.copy()
        
        return DrawdownAnalysis(
            drawdown_series=drawdown,
            max_drawdown=max_drawdown,
            max_drawdown_start=max_dd_start_idx,
            max_drawdown_end=max_dd_idx,
            max_drawdown_duration=max_dd_duration,
            recovery_date=recovery_idx,
            avg_drawdown=avg_drawdown,
            median_drawdown=median_drawdown,
            drawdown_frequency=drawdown_frequency,
            avg_recovery_time=avg_recovery_time,
            drawdown_periods=drawdown_periods,
            underwater_curve=underwater_curve
        )
    
    def _identify_drawdown_periods(self, drawdown: pd.Series, cumulative_returns: pd.Series) -> List[Dict[str, Any]]:
        """Identify individual drawdown periods."""
        periods = []
        in_drawdown = False
        current_period = None
        
        for i, (idx, dd) in enumerate(drawdown.items()):
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                peak_idx = cumulative_returns.loc[:idx].idxmax()
                current_period = {
                    'start_date': peak_idx,
                    'trough_date': idx,
                    'peak_value': cumulative_returns.loc[peak_idx],
                    'trough_value': cumulative_returns.loc[idx],
                    'drawdown': dd,
                    'duration': 0,
                    'recovery_date': None,
                    'recovery_time': None
                }
            elif dd < 0 and in_drawdown:
                # Continue drawdown - update if deeper
                if dd < current_period['drawdown']:
                    current_period['trough_date'] = idx
                    current_period['trough_value'] = cumulative_returns.loc[idx]
                    current_period['drawdown'] = dd
            elif dd >= 0 and in_drawdown:
                # Recovery
                in_drawdown = False
                current_period['recovery_date'] = idx
                current_period['duration'] = (current_period['trough_date'] - current_period['start_date']).days
                current_period['recovery_time'] = (idx - current_period['trough_date']).days
                periods.append(current_period)
                current_period = None
        
        # Handle case where we end in drawdown
        if in_drawdown and current_period:
            current_period['duration'] = (current_period['trough_date'] - current_period['start_date']).days
            periods.append(current_period)
        
        return periods
    
    def analyze_trades(self, trades: pd.DataFrame) -> TradeAnalysis:
        """Detailed trade analysis.
        
        Args:
            trades: DataFrame with trade data
            
        Returns:
            Trade analysis
        """
        if trades.empty:
            raise ValueError("Trades DataFrame is empty")
        
        # Ensure required columns
        required_cols = ['entry_time', 'exit_time', 'pnl']
        missing_cols = [col for col in required_cols if col not in trades.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Basic statistics
        total_trades = len(trades)
        winning_trades = len(trades[trades['pnl'] > 0])
        losing_trades = len(trades[trades['pnl'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Return statistics
        avg_return = trades['pnl'].mean()
        avg_win = trades[trades['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades[trades['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        best_trade = trades['pnl'].max()
        worst_trade = trades['pnl'].min()
        
        # Risk metrics
        total_wins = trades[trades['pnl'] > 0]['pnl'].sum()
        total_losses = abs(trades[trades['pnl'] < 0]['pnl'].sum())
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        expectancy = avg_return
        
        # Duration analysis
        trades['duration'] = (trades['exit_time'] - trades['entry_time']).dt.total_seconds() / 3600  # Hours
        avg_trade_duration = trades['duration'].mean()
        avg_winning_duration = trades[trades['pnl'] > 0]['duration'].mean() if winning_trades > 0 else 0
        avg_losing_duration = trades[trades['pnl'] < 0]['duration'].mean() if losing_trades > 0 else 0
        
        # Consecutive trades analysis
        max_consecutive_wins = self._calculate_max_consecutive(trades['pnl'] > 0)
        max_consecutive_losses = self._calculate_max_consecutive(trades['pnl'] < 0)
        
        # Monthly analysis
        trades['month'] = trades['exit_time'].dt.to_period('M')
        monthly_returns = trades.groupby('month')['pnl'].sum()
        monthly_win_rate = trades.groupby('month').apply(lambda x: (x['pnl'] > 0).mean())
        
        # Distribution analysis
        return_distribution = trades['pnl']
        duration_distribution = trades['duration']
        
        return TradeAnalysis(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_return=avg_return,
            avg_win=avg_win,
            avg_loss=avg_loss,
            best_trade=best_trade,
            worst_trade=worst_trade,
            profit_factor=profit_factor,
            expectancy=expectancy,
            avg_trade_duration=avg_trade_duration,
            avg_winning_duration=avg_winning_duration,
            avg_losing_duration=avg_losing_duration,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            monthly_returns=monthly_returns,
            monthly_win_rate=monthly_win_rate,
            return_distribution=return_distribution,
            duration_distribution=duration_distribution
        )
    
    def _calculate_max_consecutive(self, boolean_series: pd.Series) -> int:
        """Calculate maximum consecutive True values."""
        max_consecutive = 0
        current_consecutive = 0
        
        for value in boolean_series:
            if value:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def analyze_risk(self, returns: pd.Series, confidence_levels: List[float] = [0.95, 0.99, 0.999]) -> RiskAnalysis:
        """Comprehensive risk analysis.
        
        Args:
            returns: Daily returns series
            confidence_levels: VaR confidence levels
            
        Returns:
            Risk analysis
        """
        returns = self._validate_returns(returns)
        
        # Volatility measures
        daily_vol = returns.std()
        monthly_vol = daily_vol * np.sqrt(21)  # Approximate monthly
        annual_vol = daily_vol * np.sqrt(252)  # Approximate annual
        
        # Value at Risk for different confidence levels
        var_values = {}
        cvar_values = {}
        
        for conf in confidence_levels:
            alpha = 1 - conf
            var = np.percentile(returns, alpha * 100)
            cvar = returns[returns <= var].mean() if len(returns[returns <= var]) > 0 else var
            
            var_values[f'var_{int(conf*100)}'] = var
            cvar_values[f'cvar_{int(conf*100)}'] = cvar
        
        # Distribution analysis
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)
        
        # Normality test
        jb_stat, jb_pvalue = stats.jarque_bera(returns)
        is_normal = jb_pvalue > 0.05
        
        # Tail ratio (ratio of extreme losses to extreme gains)
        extreme_losses = returns[returns <= np.percentile(returns, 5)]
        extreme_gains = returns[returns >= np.percentile(returns, 95)]
        tail_ratio = abs(extreme_losses.mean()) / extreme_gains.mean() if extreme_gains.mean() > 0 else 0
        
        # Stress scenarios
        stress_scenarios = {
            'market_crash_2008': self._apply_stress_scenario(returns, -0.20),  # -20% shock
            'flash_crash': self._apply_stress_scenario(returns, -0.10),       # -10% shock
            'volatility_spike': self._apply_volatility_stress(returns, 2.0),  # 2x volatility
            'correlation_breakdown': self._apply_correlation_stress(returns)   # Correlation stress
        }
        
        # Risk contribution (simplified)
        risk_contribution = {
            'systematic_risk': 0.6,  # Placeholder - would need factor model
            'idiosyncratic_risk': 0.4
        }
        
        return RiskAnalysis(
            daily_volatility=daily_vol,
            monthly_volatility=monthly_vol,
            annualized_volatility=annual_vol,
            var_95=var_values.get('var_95', 0),
            var_99=var_values.get('var_99', 0),
            var_99_9=var_values.get('var_999', 0),
            cvar_95=cvar_values.get('cvar_95', 0),
            cvar_99=cvar_values.get('cvar_99', 0),
            cvar_99_9=cvar_values.get('cvar_999', 0),
            skewness=skewness,
            kurtosis=kurtosis,
            jarque_bera_stat=jb_stat,
            jarque_bera_pvalue=jb_pvalue,
            is_normal=is_normal,
            tail_ratio=tail_ratio,
            stress_scenarios=stress_scenarios,
            risk_contribution=risk_contribution
        )
    
    def _apply_stress_scenario(self, returns: pd.Series, shock: float) -> float:
        """Apply stress scenario to returns."""
        stressed_returns = returns + shock
        return stressed_returns.sum()
    
    def _apply_volatility_stress(self, returns: pd.Series, vol_multiplier: float) -> float:
        """Apply volatility stress to returns."""
        mean_return = returns.mean()
        stressed_returns = mean_return + (returns - mean_return) * vol_multiplier
        return stressed_returns.sum()
    
    def _apply_correlation_stress(self, returns: pd.Series) -> float:
        """Apply correlation stress scenario."""
        # Simplified correlation stress - assume correlations go to 1 in crisis
        # This would be more complex with multiple assets
        return returns.sum() * 1.2  # 20% increase in losses
    
    def generate_report(
        self,
        metrics: PerformanceMetrics,
        output_path: Optional[Path] = None
    ) -> str:
        """Generate comprehensive performance report.
        
        Args:
            metrics: Performance metrics
            output_path: Optional output file path
            
        Returns:
            Report as string
        """
        report_lines = []
        
        # Header
        report_lines.append("=" * 80)
        report_lines.append("CRYPTOCURRENCY BACKTESTING PERFORMANCE REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Analysis Period: {metrics.analysis_start} to {metrics.analysis_end}")
        report_lines.append(f"Total Days: {metrics.analysis_period_days}")
        report_lines.append("")
        
        # Return Metrics
        report_lines.append("RETURN METRICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Total Return: {metrics.total_return:.2%}")
        report_lines.append(f"Annualized Return: {metrics.annualized_return:.2%}")
        report_lines.append(f"Cumulative Return: {metrics.cumulative_return:.2%}")
        report_lines.append("")
        
        # Risk Metrics
        report_lines.append("RISK METRICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Volatility (Annualized): {metrics.volatility:.2%}")
        report_lines.append(f"Sharpe Ratio: {metrics.sharpe_ratio:.3f}")
        report_lines.append(f"Sortino Ratio: {metrics.sortino_ratio:.3f}")
        report_lines.append(f"Calmar Ratio: {metrics.calmar_ratio:.3f}")
        report_lines.append("")
        
        # Drawdown Metrics
        report_lines.append("DRAWDOWN METRICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Maximum Drawdown: {metrics.max_drawdown:.2%}")
        report_lines.append(f"Max Drawdown Duration: {metrics.max_drawdown_duration} days")
        report_lines.append(f"Average Drawdown: {metrics.avg_drawdown:.2%}")
        report_lines.append(f"Recovery Time: {metrics.drawdown_recovery_time:.1f} days")
        report_lines.append("")
        
        # Risk Measures
        report_lines.append("RISK MEASURES")
        report_lines.append("-" * 40)
        report_lines.append(f"Value at Risk (95%): {metrics.value_at_risk_95:.2%}")
        report_lines.append(f"Value at Risk (99%): {metrics.value_at_risk_99:.2%}")
        report_lines.append(f"Conditional VaR (95%): {metrics.conditional_var_95:.2%}")
        report_lines.append(f"Conditional VaR (99%): {metrics.conditional_var_99:.2%}")
        report_lines.append("")
        
        # Distribution Metrics
        report_lines.append("DISTRIBUTION METRICS")
        report_lines.append("-" * 40)
        report_lines.append(f"Skewness: {metrics.skewness:.3f}")
        report_lines.append(f"Kurtosis: {metrics.kurtosis:.3f}")
        report_lines.append("")
        
        # Benchmark Comparison (if available)
        if metrics.beta is not None:
            report_lines.append("BENCHMARK COMPARISON")
            report_lines.append("-" * 40)
            report_lines.append(f"Beta: {metrics.beta:.3f}")
            report_lines.append(f"Alpha: {metrics.alpha:.2%}")
            report_lines.append(f"Correlation: {metrics.correlation:.3f}")
            report_lines.append(f"Tracking Error: {metrics.tracking_error:.2%}")
            report_lines.append(f"Information Ratio: {metrics.information_ratio:.3f}")
            report_lines.append("")
        
        # Trade Metrics (if available)
        if metrics.total_trades > 0:
            report_lines.append("TRADE METRICS")
            report_lines.append("-" * 40)
            report_lines.append(f"Total Trades: {metrics.total_trades}")
            report_lines.append(f"Winning Trades: {metrics.winning_trades}")
            report_lines.append(f"Losing Trades: {metrics.losing_trades}")
            report_lines.append(f"Win Rate: {metrics.win_rate:.2%}")
            report_lines.append(f"Average Win: {metrics.avg_win:.2%}")
            report_lines.append(f"Average Loss: {metrics.avg_loss:.2%}")
            report_lines.append(f"Profit Factor: {metrics.profit_factor:.2f}")
            report_lines.append("")
        
        report_lines.append("=" * 80)
        
        report = "\n".join(report_lines)
        
        # Save to file if path provided
        if output_path:
            output_path.write_text(report)
            self.logger.info(f"Report saved to {output_path}")
        
        return report


class ReportGenerator:
    """Generates various types of performance reports and visualizations."""
    
    def __init__(self):
        """Initialize report generator."""
        self.logger = get_logger("report_generator")
        
        # Set plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def create_performance_dashboard(
        self,
        returns: pd.Series,
        metrics: PerformanceMetrics,
        benchmark_returns: Optional[pd.Series] = None,
        output_path: Optional[Path] = None
    ) -> Figure:
        """Create comprehensive performance dashboard.
        
        Args:
            returns: Daily returns series
            metrics: Performance metrics
            benchmark_returns: Optional benchmark returns
            output_path: Optional output file path
            
        Returns:
            Matplotlib figure
        """
        # Create figure with subplots
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Performance Dashboard', fontsize=16, fontweight='bold')
        
        # 1. Cumulative Returns
        cumulative_returns = (1 + returns).cumprod()
        axes[0, 0].plot(cumulative_returns.index, cumulative_returns.values, label='Strategy', linewidth=2)
        
        if benchmark_returns is not None:
            benchmark_cumulative = (1 + benchmark_returns).cumprod()
            axes[0, 0].plot(benchmark_cumulative.index, benchmark_cumulative.values, 
                          label='Benchmark', linewidth=2, alpha=0.7)
        
        axes[0, 0].set_title('Cumulative Returns')
        axes[0, 0].set_ylabel('Cumulative Return')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Drawdown
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        
        axes[0, 1].fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
        axes[0, 1].plot(drawdown.index, drawdown.values, color='red', linewidth=1)
        axes[0, 1].set_title('Drawdown')
        axes[0, 1].set_ylabel('Drawdown')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Rolling Sharpe Ratio
        rolling_sharpe = returns.rolling(window=252).apply(
            lambda x: x.mean() / x.std() * np.sqrt(252) if x.std() > 0 else 0
        )
        axes[0, 2].plot(rolling_sharpe.index, rolling_sharpe.values, linewidth=2)
        axes[0, 2].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[0, 2].set_title('Rolling Sharpe Ratio (1Y)')
        axes[0, 2].set_ylabel('Sharpe Ratio')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. Return Distribution
        axes[1, 0].hist(returns.values, bins=50, alpha=0.7, density=True)
        axes[1, 0].axvline(returns.mean(), color='red', linestyle='--', label=f'Mean: {returns.mean():.3f}')
        axes[1, 0].axvline(returns.median(), color='green', linestyle='--', label=f'Median: {returns.median():.3f}')
        axes[1, 0].set_title('Return Distribution')
        axes[1, 0].set_xlabel('Daily Returns')
        axes[1, 0].set_ylabel('Density')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. Monthly Returns Heatmap
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        monthly_returns.index = monthly_returns.index.to_period('M')
        
        # Create pivot table for heatmap
        monthly_df = pd.DataFrame({
            'Year': monthly_returns.index.year,
            'Month': monthly_returns.index.month,
            'Return': monthly_returns.values
        })
        
        if len(monthly_df) > 0:
            pivot_table = monthly_df.pivot(index='Year', columns='Month', values='Return')
            sns.heatmap(pivot_table, annot=True, fmt='.1%', cmap='RdYlGn', center=0, 
                       ax=axes[1, 1], cbar_kws={'label': 'Monthly Return'})
            axes[1, 1].set_title('Monthly Returns Heatmap')
        else:
            axes[1, 1].text(0.5, 0.5, 'Insufficient data for monthly heatmap', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Monthly Returns Heatmap')
        
        # 6. Key Metrics Table
        axes[1, 2].axis('off')
        metrics_data = [
            ['Total Return', f"{metrics.total_return:.2%}"],
            ['Annualized Return', f"{metrics.annualized_return:.2%}"],
            ['Volatility', f"{metrics.volatility:.2%}"],
            ['Sharpe Ratio', f"{metrics.sharpe_ratio:.3f}"],
            ['Max Drawdown', f"{metrics.max_drawdown:.2%}"],
            ['Calmar Ratio', f"{metrics.calmar_ratio:.3f}"],
            ['VaR (95%)', f"{metrics.value_at_risk_95:.2%}"],
            ['Skewness', f"{metrics.skewness:.3f}"],
            ['Kurtosis', f"{metrics.kurtosis:.3f}"]
        ]
        
        table = axes[1, 2].table(cellText=metrics_data, 
                                colLabels=['Metric', 'Value'],
                                cellLoc='center',
                                loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        axes[1, 2].set_title('Key Metrics')
        
        plt.tight_layout()
        
        # Save if path provided
        if output_path:
            fig.savefig(output_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"Dashboard saved to {output_path}")
        
        return fig


# Convenience functions
def analyze_performance(
    returns: pd.Series,
    benchmark_returns: Optional[pd.Series] = None,
    trades: Optional[pd.DataFrame] = None,
    risk_free_rate: float = 0.02
) -> PerformanceMetrics:
    """Convenience function for performance analysis."""
    analyzer = PerformanceAnalyzer(risk_free_rate=risk_free_rate)
    return analyzer.analyze_returns(returns, benchmark_returns, trades)


def create_performance_report(
    returns: pd.Series,
    output_dir: Path,
    benchmark_returns: Optional[pd.Series] = None,
    trades: Optional[pd.DataFrame] = None
) -> Tuple[PerformanceMetrics, str, Figure]:
    """Create comprehensive performance report with dashboard."""
    # Analyze performance
    metrics = analyze_performance(returns, benchmark_returns, trades)
    
    # Generate text report
    analyzer = PerformanceAnalyzer()
    report_text = analyzer.generate_report(metrics, output_dir / "performance_report.txt")
    
    # Create dashboard
    report_gen = ReportGenerator()
    dashboard = report_gen.create_performance_dashboard(
        returns, metrics, benchmark_returns, output_dir / "performance_dashboard.png"
    )
    
    return metrics, report_text, dashboard