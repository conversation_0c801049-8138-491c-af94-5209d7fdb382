# Testing Configuration for Crypto Backtesting System
# This configuration is optimized for unit tests, integration tests, and CI/CD

# Application settings
app:
  name: "crypto-backtester-test"
  version: "0.1.0"
  environment: "testing"
  debug: true
  timezone: "UTC"

# Logging configuration - Minimal for tests
logging:
  level: "WARNING"  # Reduce noise in tests
  format: "%(levelname)s - %(name)s - %(message)s"
  file: "logs/testing.log"
  max_size: "50MB"
  backup_count: 2
  console: true
  colored: false  # Better for CI/CD
  
  # Module-specific logging levels
  loggers:
    backtrader: "ERROR"
    ccxt: "ERROR"
    urllib3: "ERROR"
    requests: "ERROR"
    pytest: "INFO"

# Hardware optimization - Conservative for testing
hardware:
  cpu:
    cores: 16
    threads: 32
    workers: 4  # Limited for stable tests
    affinity: null
    
  memory:
    total_gb: 96
    available_gb: 8  # Limited for testing
    chunk_size_mb: 64  # Small chunks
    cache_size_gb: 1
    gc_threshold: 0.7
    
  gpu:
    enabled: false  # Disabled for consistent testing
    device: "cpu"
    memory_gb: 0

# Parallel processing - Limited for test stability
parallel:
  enabled: true
  max_workers: 2  # Minimal parallelism
  chunk_size: 100
  timeout: 300  # 5 minutes
  memory_limit_gb: 1  # Per worker
  
  # Process pool settings
  pool:
    type: "thread"  # Threads for faster test startup
    initializer: null
    maxtasksperchild: 10
    
  # Task scheduling
  scheduler:
    strategy: "round_robin"
    queue_size: 100
    batch_size: 5

# Data management - Test data
data:
  # Storage paths - Use test directories
  paths:
    raw: "tests/data/raw"
    processed: "tests/data/processed"
    cache: "tests/data/cache"
    results: "tests/data/results"
    
  # Data sources - Mock/test exchanges
  exchanges:
    binance:
      enabled: true
      api_key: "test_api_key"
      api_secret: "test_api_secret"
      sandbox: true
      rate_limit: 10  # Low rate limit for tests
      
    mock_exchange:
      enabled: true
      api_key: "mock_key"
      api_secret: "mock_secret"
      
  # Data quality - Relaxed for test data
  quality:
    min_data_points: 10
    max_missing_ratio: 0.1
    outlier_threshold: 5.0
    validate_ohlcv: false  # Skip validation for speed
    
  # Caching - Disabled for consistent tests
  cache:
    enabled: false
    backend: "memory"
    ttl: 60
    compression: "none"
    
# Backtesting engine - Test settings
engine:
  # Default settings
  initial_cash: 10000  # Small amount for tests
  commission: 0.001
  slippage: 0.0
  
  # Risk management - Relaxed for testing
  risk:
    max_position_size: 1.0  # Allow full position
    max_drawdown: 1.0  # No limit
    stop_loss: 0.0  # Disabled
    take_profit: 0.0  # Disabled
    
  # Performance tracking
  metrics:
    calculate_all: false  # Only basic metrics
    benchmark: null
    risk_free_rate: 0.0
    
# Strategy configuration - Test strategies
strategies:
  # Default parameters
  defaults:
    timeframe: "1h"
    lookback: 20  # Short lookback for tests
    min_periods: 5
    
  # Strategy registry - Simple test strategies
  registry:
    test_strategy:
      class: "TestStrategy"
      params:
        period: 10
        
    mock_ma_crossover:
      class: "MockMovingAverageCrossover"
      params:
        fast_period: 5
        slow_period: 10
        
    simple_buy_hold:
      class: "SimpleBuyHoldStrategy"
      params: {}
        
# Optimization settings - Minimal for tests
optimization:
  # Algorithm settings
  algorithms:
    grid_search:
      enabled: true
      max_combinations: 10  # Very limited
      
    bayesian:
      enabled: false  # Disabled for speed
      
    genetic:
      enabled: false  # Disabled for speed
      
  # Objectives
  objectives:
    primary: "total_return"
    secondary: null
    constraints: {}
      
  # Performance thresholds - Relaxed
  thresholds:
    min_sharpe: -10.0
    min_win_rate: 0.0
    max_drawdown: 1.0
    
# Monitoring and alerting - Disabled
monitoring:
  enabled: false
  interval: 60
  
  # System metrics
  metrics:
    cpu: false
    memory: false
    disk: false
    gpu: false
    network: false
    
  # Alerts
  alerts:
    cpu_threshold: 1.0
    memory_threshold: 1.0
    disk_threshold: 1.0
    
  # Dashboard
  dashboard:
    enabled: false
    
# Database configuration - In-memory/SQLite
database:
  # Results storage
  results:
    type: "sqlite"
    path: ":memory:"  # In-memory database
    
  # Cache storage
  cache:
    type: "memory"
    
# Security settings - Minimal
security:
  # API keys - Test values
  env_vars: []
    
  # Encryption
  encryption:
    enabled: false
    
# Testing specific settings
testing:
  # Test data
  data:
    generate_mock_data: true
    mock_data_points: 1000
    mock_symbols: ["BTC/USDT", "ETH/USDT"]
    mock_timeframes: ["1h", "4h"]
    
  # Test execution
  execution:
    fast_mode: true
    skip_slow_tests: false
    parallel_tests: true
    test_timeout: 30  # seconds
    
  # Fixtures
  fixtures:
    data_dir: "tests/fixtures/data"
    strategy_dir: "tests/fixtures/strategies"
    config_dir: "tests/fixtures/config"
    
  # Coverage
  coverage:
    enabled: true
    threshold: 80
    exclude_patterns:
      - "*/tests/*"
      - "*/venv/*"
      - "setup.py"
      
  # Performance testing
  performance:
    enabled: false  # Disabled by default
    max_execution_time: 10  # seconds
    memory_limit_mb: 500
    
# Mock services
mocks:
  # Exchange APIs
  exchanges:
    enabled: true
    response_delay: 0.1  # seconds
    error_rate: 0.0  # No errors by default
    
  # Data providers
  data_providers:
    enabled: true
    static_data: true
    
  # External services
  external_services:
    enabled: true
    
# Feature flags - Testing specific
features:
  gpu_acceleration: false
  parallel_optimization: false
  real_time_monitoring: false
  advanced_metrics: false
  machine_learning: false
  mock_mode: true
  
# CI/CD specific settings
ci_cd:
  # Environment detection
  detect_ci: true
  ci_environments:
    - "GITHUB_ACTIONS"
    - "TRAVIS"
    - "JENKINS"
    - "GITLAB_CI"
    
  # Resource limits for CI
  limits:
    max_workers: 2
    memory_limit_gb: 2
    timeout_minutes: 30
    
  # Reporting
  reporting:
    junit_xml: true
    coverage_xml: true
    html_reports: false
    
# Test categories
test_categories:
  unit:
    enabled: true
    timeout: 5  # seconds per test
    
  integration:
    enabled: true
    timeout: 30  # seconds per test
    
  performance:
    enabled: false  # Disabled by default
    timeout: 300  # seconds per test
    
  end_to_end:
    enabled: false  # Disabled by default
    timeout: 600  # seconds per test
    
# Test data generation
test_data_generation:
  # OHLCV data
  ohlcv:
    start_price: 50000  # Starting price for BTC
    volatility: 0.02  # 2% daily volatility
    trend: 0.0001  # Slight upward trend
    noise: 0.001  # Random noise
    
  # Market conditions
  market_conditions:
    - "trending_up"
    - "trending_down"
    - "sideways"
    - "volatile"
    
  # Time periods
  time_periods:
    short: 100  # data points
    medium: 500
    long: 1000
    
# Cleanup settings
cleanup:
  # Temporary files
  temp_files:
    enabled: true
    max_age_hours: 1
    
  # Test databases
  test_databases:
    enabled: true
    cleanup_after_test: true
    
  # Log files
  log_files:
    enabled: true
    max_size_mb: 10