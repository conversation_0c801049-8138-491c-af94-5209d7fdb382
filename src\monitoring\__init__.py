"""System monitoring module for cryptocurrency backtesting system.

This module provides comprehensive system monitoring capabilities including
performance tracking, resource monitoring, alerting, and real-time dashboards
optimized for high-performance trading systems.

Classes:
    SystemMonitor: Main system monitoring coordinator
    PerformanceTracker: Track backtesting performance metrics
    ResourceMonitor: Monitor CPU, memory, disk, and network usage
    AlertManager: Manage alerts and notifications
    Dashboard: Real-time monitoring dashboard
    MetricsCollector: Collect and aggregate system metrics

Functions:
    start_monitoring: Start system monitoring
    get_system_stats: Get current system statistics
    create_alert: Create monitoring alert
    generate_report: Generate monitoring report

Example:
    >>> from src.monitoring import SystemMonitor, AlertManager
    >>> 
    >>> # Start comprehensive monitoring
    >>> monitor = SystemMonitor()
    >>> monitor.start()
    >>> 
    >>> # Setup alerts for resource thresholds
    >>> alert_mgr = AlertManager()
    >>> alert_mgr.add_alert('cpu_high', threshold=90, action='log')
    >>> alert_mgr.add_alert('memory_critical', threshold=95, action='email')
    >>> 
    >>> # Get real-time system stats
    >>> stats = monitor.get_current_stats()
    >>> print(f"CPU Usage: {stats['cpu_percent']:.1f}%")
    >>> print(f"Memory Usage: {stats['memory_percent']:.1f}%")
"""

# Import monitoring components
try:
    from .system_monitor import SystemMonitor
    from .performance_tracker import PerformanceTracker
    from .resource_monitor import ResourceMonitor
    from .alert_manager import AlertManager
    from .dashboard import Dashboard
    from .metrics_collector import MetricsCollector
    from .logger import MonitoringLogger
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Monitoring configuration optimized for Ryzen 9 7950X
MONITORING_CONFIG = {
    'update_interval': 5,           # 5 second updates
    'history_retention': 3600,      # 1 hour history
    'cpu_cores': 16,                # 16 physical cores
    'logical_cores': 32,            # 32 logical cores with hyperthreading
    'memory_total_gb': 96,          # 96GB DDR5 RAM
    'enable_per_core_monitoring': True,
    'enable_gpu_monitoring': True,   # RTX 5060Ti monitoring
    'enable_disk_monitoring': True,
    'enable_network_monitoring': True,
    'enable_temperature_monitoring': True
}

# Resource monitoring thresholds
RESOURCE_THRESHOLDS = {
    'cpu': {
        'warning': 80,              # 80% CPU usage warning
        'critical': 95,             # 95% CPU usage critical
        'sustained_duration': 300   # 5 minutes sustained high usage
    },
    'memory': {
        'warning': 75,              # 75% memory usage warning (72GB)
        'critical': 90,             # 90% memory usage critical (86.4GB)
        'leak_detection': True,     # Enable memory leak detection
        'leak_threshold_mb': 100    # 100MB/hour leak threshold
    },
    'disk': {
        'warning': 85,              # 85% disk usage warning
        'critical': 95,             # 95% disk usage critical
        'io_warning': 80,           # 80% I/O utilization warning
        'io_critical': 95           # 95% I/O utilization critical
    },
    'gpu': {
        'warning': 85,              # 85% GPU usage warning
        'critical': 95,             # 95% GPU usage critical
        'memory_warning': 80,       # 80% GPU memory warning
        'memory_critical': 95,      # 95% GPU memory critical
        'temp_warning': 80,         # 80°C temperature warning
        'temp_critical': 90         # 90°C temperature critical
    },
    'network': {
        'bandwidth_warning': 80,    # 80% bandwidth usage warning
        'latency_warning': 100,     # 100ms latency warning
        'packet_loss_warning': 1    # 1% packet loss warning
    }
}

# Performance metrics to track
PERFORMANCE_METRICS = [
    'backtest_duration',
    'strategies_per_second',
    'parameter_combinations_per_minute',
    'data_processing_rate',
    'memory_efficiency',
    'cpu_efficiency',
    'parallel_efficiency',
    'cache_hit_rate',
    'disk_io_rate',
    'network_throughput'
]

# Alert types and actions
ALERT_TYPES = {
    'resource_threshold': 'Resource usage threshold exceeded',
    'performance_degradation': 'Performance degradation detected',
    'system_error': 'System error occurred',
    'memory_leak': 'Memory leak detected',
    'disk_space_low': 'Disk space running low',
    'temperature_high': 'High temperature detected',
    'process_crash': 'Process crash detected'
}

ALERT_ACTIONS = {
    'log': 'Log alert to file',
    'console': 'Print alert to console',
    'email': 'Send email notification',
    'webhook': 'Send webhook notification',
    'restart': 'Restart affected process',
    'cleanup': 'Trigger cleanup procedures',
    'throttle': 'Throttle system resources'
}

# Dashboard configuration
DASHBOARD_CONFIG = {
    'refresh_rate': 5,              # 5 second refresh
    'chart_history': 300,           # 5 minutes of chart history
    'enable_real_time': True,       # Real-time updates
    'enable_alerts': True,          # Show alerts on dashboard
    'enable_predictions': True,     # Show performance predictions
    'theme': 'dark',                # Dashboard theme
    'port': 8080                    # Dashboard web port
}

# Metrics collection settings
METRICS_COLLECTION = {
    'collection_interval': 1,       # 1 second collection interval
    'aggregation_interval': 60,     # 1 minute aggregation
    'retention_period': 86400,      # 24 hours retention
    'compression_enabled': True,    # Compress historical data
    'export_format': 'prometheus',  # Metrics export format
    'export_endpoint': '/metrics'   # Metrics export endpoint
}

# System health checks
HEALTH_CHECKS = {
    'cpu_temperature': {
        'enabled': True,
        'warning_temp': 70,         # 70°C warning
        'critical_temp': 85         # 85°C critical
    },
    'memory_integrity': {
        'enabled': True,
        'check_interval': 3600      # 1 hour interval
    },
    'disk_health': {
        'enabled': True,
        'smart_monitoring': True,   # SMART disk monitoring
        'check_interval': 1800      # 30 minutes interval
    },
    'network_connectivity': {
        'enabled': True,
        'test_hosts': ['*******', '*******'],
        'check_interval': 300       # 5 minutes interval
    }
}

def start_monitoring(config=None, background=True):
    """Start system monitoring.
    
    Args:
        config: Optional monitoring configuration
        background: Whether to run monitoring in background
        
    Returns:
        SystemMonitor: System monitor instance
    """
    # This would be implemented when the actual monitoring classes are created
    raise NotImplementedError("start_monitoring will be implemented with monitoring classes")

def get_system_stats():
    """Get current system statistics.
    
    Returns:
        dict: Current system statistics
    """
    # This would be implemented when the actual monitoring classes are created
    raise NotImplementedError("get_system_stats will be implemented with monitoring classes")

def create_alert(name, threshold, action, **kwargs):
    """Create monitoring alert.
    
    Args:
        name: Alert name
        threshold: Alert threshold value
        action: Action to take when alert triggers
        **kwargs: Additional alert parameters
    """
    # This would be implemented when the actual alert classes are created
    raise NotImplementedError("create_alert will be implemented with alert classes")

def generate_report(start_time=None, end_time=None, format='html'):
    """Generate monitoring report.
    
    Args:
        start_time: Report start time
        end_time: Report end time
        format: Report format ('html', 'pdf', 'json')
        
    Returns:
        str: Report content or file path
    """
    # This would be implemented when the actual report classes are created
    raise NotImplementedError("generate_report will be implemented with report classes")

# Export public API
__all__ = [
    "SystemMonitor",
    "PerformanceTracker",
    "ResourceMonitor",
    "AlertManager",
    "Dashboard",
    "MetricsCollector",
    "MonitoringLogger",
    "start_monitoring",
    "get_system_stats",
    "create_alert",
    "generate_report",
    "MONITORING_CONFIG",
    "RESOURCE_THRESHOLDS",
    "PERFORMANCE_METRICS",
    "ALERT_TYPES",
    "ALERT_ACTIONS",
    "DASHBOARD_CONFIG",
    "METRICS_COLLECTION",
    "HEALTH_CHECKS",
]