"""Parameter optimization module for cryptocurrency backtesting system.

This module provides advanced optimization algorithms for discovering alpha parameters
without compromising backtesting quality. Features parallel processing capabilities
and multiple optimization strategies.

Classes:
    ParallelOptimizer: Multi-process parameter optimization
    GridSearchOptimizer: Exhaustive grid search optimization
    BayesianOptimizer: Bayesian optimization for efficient parameter search
    GeneticOptimizer: Genetic algorithm-based optimization
    OptimizationResult: Container for optimization results
    ParameterSpace: Define parameter search spaces

Functions:
    optimize_strategy: Convenience function for strategy optimization
    create_parameter_grid: Create parameter grid from ranges
    evaluate_parameters: Evaluate parameter set performance

Example:
    >>> from src.optimization import ParallelOptimizer, ParameterSpace
    >>> from src.strategies import MovingAverageCrossover
    >>> 
    >>> # Define parameter space
    >>> param_space = ParameterSpace({
    >>>     'fast_period': (5, 50, 5),  # min, max, step
    >>>     'slow_period': (20, 200, 10)
    >>> })
    >>> 
    >>> # Run parallel optimization
    >>> optimizer = ParallelOptimizer(max_workers=16)
    >>> results = optimizer.optimize(
    >>>     strategy_class=MovingAverageCrossover,
    >>>     data=market_data,
    >>>     param_space=param_space,
    >>>     objective='sharpe_ratio'
    >>> )
    >>> 
    >>> # Get best parameters
    >>> best_params = results.get_best_parameters()
    >>> print(f"Best Sharpe ratio: {results.best_score:.4f}")
    >>> print(f"Best parameters: {best_params}")
"""

# Import optimization classes
try:
    from .parallel_optimizer import ParallelOptimizer
    from .grid_search import GridSearchOptimizer
    from .bayesian_optimizer import BayesianOptimizer
    from .genetic_optimizer import GeneticOptimizer
    from .result import OptimizationResult
    from .parameter_space import ParameterSpace
    from .objectives import ObjectiveFunction
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Optimization algorithms
OPTIMIZATION_ALGORITHMS = {
    'grid_search': 'GridSearchOptimizer',
    'bayesian': 'BayesianOptimizer', 
    'genetic': 'GeneticOptimizer',
    'parallel_grid': 'ParallelOptimizer'
}

# Objective functions
OBJECTIVE_FUNCTIONS = {
    'sharpe_ratio': 'maximize',
    'total_return': 'maximize',
    'max_drawdown': 'minimize',
    'win_rate': 'maximize',
    'profit_factor': 'maximize',
    'calmar_ratio': 'maximize',
    'sortino_ratio': 'maximize'
}

# Optimization constraints
DEFAULT_CONSTRAINTS = {
    'min_trades': 10,           # Minimum number of trades
    'max_drawdown': 0.3,        # Maximum allowed drawdown (30%)
    'min_sharpe': 0.5,          # Minimum Sharpe ratio
    'min_win_rate': 0.3,        # Minimum win rate (30%)
    'max_correlation': 0.8      # Maximum correlation with benchmark
}

# Performance thresholds for alpha discovery
ALPHA_THRESHOLDS = {
    'excellent': {'sharpe': 2.0, 'return': 0.3, 'drawdown': 0.1},
    'good': {'sharpe': 1.5, 'return': 0.2, 'drawdown': 0.15},
    'acceptable': {'sharpe': 1.0, 'return': 0.1, 'drawdown': 0.2},
    'poor': {'sharpe': 0.5, 'return': 0.05, 'drawdown': 0.25}
}

# Parallel processing settings
DEFAULT_MAX_WORKERS = 16
DEFAULT_CHUNK_SIZE = 100
DEFAULT_TIMEOUT = 3600  # 1 hour

# Memory management
MAX_MEMORY_PER_WORKER = 4  # GB
MEMORY_CLEANUP_INTERVAL = 100  # iterations

def optimize_strategy(strategy_class, data, param_space, algorithm='parallel_grid', **kwargs):
    """Convenience function for strategy optimization.
    
    Args:
        strategy_class: Strategy class to optimize
        data: Market data for backtesting
        param_space: Parameter space definition
        algorithm: Optimization algorithm to use
        **kwargs: Additional optimization parameters
        
    Returns:
        OptimizationResult: Optimization results
    """
    if algorithm not in OPTIMIZATION_ALGORITHMS:
        raise ValueError(f"Unknown algorithm: {algorithm}. Available: {list(OPTIMIZATION_ALGORITHMS.keys())}")
    
    # This would be implemented when the actual optimizer classes are created
    raise NotImplementedError("optimize_strategy will be implemented with optimizer classes")

# Export public API
__all__ = [
    "ParallelOptimizer",
    "GridSearchOptimizer",
    "BayesianOptimizer",
    "GeneticOptimizer",
    "OptimizationResult",
    "ParameterSpace",
    "ObjectiveFunction",
    "optimize_strategy",
    "OPTIMIZATION_ALGORITHMS",
    "OBJECTIVE_FUNCTIONS",
    "DEFAULT_CONSTRAINTS",
    "ALPHA_THRESHOLDS",
    "DEFAULT_MAX_WORKERS",
    "DEFAULT_CHUNK_SIZE",
    "DEFAULT_TIMEOUT",
]