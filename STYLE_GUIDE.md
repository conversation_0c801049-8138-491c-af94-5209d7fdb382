# Crypto Backtesting System - Style Guide

## Code Style and Standards

### Python Style Guidelines

#### PEP 8 Compliance
- Follow PEP 8 standards for all Python code
- Use `black` formatter with line length of 88 characters
- Use `isort` for import sorting
- Use `flake8` for linting with the following configuration:

```ini
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist
```

#### Naming Conventions
- **Variables and Functions**: `snake_case`
- **Classes**: `PascalCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private Methods**: `_leading_underscore`
- **Modules**: `lowercase_with_underscores`
- **Packages**: `lowercase`

```python
# Good examples
class BacktestEngine:
    MAX_WORKERS = 32
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self._worker_pool = None
    
    def _initialize_workers(self) -> None:
        pass
    
    def run_optimization(self, strategy_params: Dict[str, Any]) -> OptimizationResult:
        pass
```

### Type Hinting

#### Mandatory Type Hints
- All function arguments must have type hints
- All function return values must have type hints
- Class attributes should have type hints when not obvious
- Use `typing` module for complex types

```python
from typing import Dict, List, Optional, Union, Tuple, Any
from pathlib import Path
import pandas as pd

def process_data(
    data: pd.DataFrame,
    parameters: Dict[str, Union[int, float]],
    output_path: Optional[Path] = None
) -> Tuple[pd.DataFrame, Dict[str, float]]:
    """Process trading data with given parameters.
    
    Args:
        data: Historical price data
        parameters: Strategy parameters
        output_path: Optional path to save results
    
    Returns:
        Tuple of processed data and performance metrics
    """
    pass
```

#### Type Hint Best Practices
- Use `Optional[T]` instead of `Union[T, None]`
- Use `List[T]` instead of `list` for Python < 3.9
- Use `Dict[K, V]` instead of `dict` for Python < 3.9
- Import types at module level, not in functions
- Use `TypeVar` for generic types when appropriate

### Documentation Standards

#### Google-Style Docstrings
All functions, classes, and modules must have comprehensive docstrings:

```python
def optimize_strategy(
    strategy_class: Type[BaseStrategy],
    data: pd.DataFrame,
    param_grid: Dict[str, List[Union[int, float]]],
    n_workers: int = 4
) -> OptimizationResult:
    """Optimize strategy parameters using parallel processing.
    
    This function performs grid search optimization across the provided
    parameter space using multiple worker processes to maximize throughput
    while maintaining result quality.
    
    Args:
        strategy_class: The strategy class to optimize
        data: Historical price data for backtesting
        param_grid: Dictionary mapping parameter names to value lists
        n_workers: Number of parallel worker processes
    
    Returns:
        OptimizationResult containing best parameters and performance metrics
    
    Raises:
        ValueError: If param_grid is empty or invalid
        MemoryError: If dataset is too large for available memory
        ProcessError: If worker processes fail to initialize
    
    Example:
        >>> strategy = MovingAverageCrossover
        >>> data = fetch_crypto_data('BTC/USD', '1d', '2020-01-01', '2023-01-01')
        >>> params = {'fast_period': [10, 20, 30], 'slow_period': [50, 100, 200]}
        >>> result = optimize_strategy(strategy, data, params, n_workers=8)
        >>> print(f"Best parameters: {result.best_params}")
    
    Note:
        This function requires significant memory and CPU resources.
        Monitor system performance during execution.
    """
    pass
```

#### Module Documentation
Every module should start with a module-level docstring:

```python
"""Parallel optimization engine for cryptocurrency trading strategies.

This module provides the core functionality for running parameter optimization
across multiple CPU cores while maintaining data integrity and result quality.
It includes memory management, process coordination, and result aggregation.

Classes:
    ParallelOptimizer: Main optimization coordinator
    WorkerProcess: Individual optimization worker
    ResultAggregator: Collects and validates results

Functions:
    create_worker_pool: Initialize worker process pool
    validate_parameters: Ensure parameter validity

Example:
    >>> from src.optimization.parallel_optimizer import ParallelOptimizer
    >>> optimizer = ParallelOptimizer(n_workers=16)
    >>> results = optimizer.optimize(strategy, data, param_grid)
"""
```

### Error Handling

#### Exception Handling Best Practices
- Use specific exception types, not bare `except:`
- Always log exceptions with context
- Provide meaningful error messages
- Use custom exceptions for domain-specific errors

```python
class BacktestError(Exception):
    """Base exception for backtesting operations."""
    pass

class DataValidationError(BacktestError):
    """Raised when input data fails validation."""
    pass

class OptimizationError(BacktestError):
    """Raised when optimization process fails."""
    pass

def load_data(file_path: Path) -> pd.DataFrame:
    """Load and validate trading data from file."""
    try:
        data = pd.read_csv(file_path)
    except FileNotFoundError:
        logger.error(f"Data file not found: {file_path}")
        raise DataValidationError(f"Cannot load data from {file_path}")
    except pd.errors.EmptyDataError:
        logger.error(f"Data file is empty: {file_path}")
        raise DataValidationError(f"Data file {file_path} contains no data")
    
    if data.empty:
        raise DataValidationError("Loaded data is empty")
    
    return data
```

### Logging Standards

#### Logging Configuration
- Use Python's `logging` module
- Configure different log levels for different components
- Include timestamps, module names, and log levels
- Use structured logging for production

```python
import logging
from typing import Optional

def setup_logging(
    level: str = "INFO",
    log_file: Optional[Path] = None,
    format_string: Optional[str] = None
) -> None:
    """Configure application logging.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file path for log output
        format_string: Custom format string for log messages
    """
    if format_string is None:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(message)s"
        )
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_string,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )

# Usage in modules
logger = logging.getLogger(__name__)

def process_optimization(params: Dict[str, Any]) -> None:
    """Process optimization with comprehensive logging."""
    logger.info(f"Starting optimization with {len(params)} parameter sets")
    
    try:
        result = run_backtest(params)
        logger.info(f"Optimization completed successfully: {result.summary}")
    except Exception as e:
        logger.error(f"Optimization failed: {str(e)}", exc_info=True)
        raise
```

### Performance Guidelines

#### Memory Management
- Use generators for large datasets
- Implement explicit garbage collection
- Monitor memory usage in long-running processes
- Use memory profiling tools during development

```python
import gc
import psutil
from typing import Iterator, Generator

def process_data_chunks(
    data: pd.DataFrame, 
    chunk_size: int = 10000
) -> Generator[pd.DataFrame, None, None]:
    """Process data in chunks to manage memory usage.
    
    Args:
        data: Full dataset
        chunk_size: Number of rows per chunk
    
    Yields:
        DataFrame chunks for processing
    """
    for i in range(0, len(data), chunk_size):
        chunk = data.iloc[i:i + chunk_size].copy()
        yield chunk
        
        # Explicit garbage collection for large datasets
        if i % (chunk_size * 10) == 0:
            gc.collect()

def monitor_memory_usage() -> float:
    """Get current memory usage percentage.
    
    Returns:
        Memory usage as percentage of total available
    """
    process = psutil.Process()
    memory_info = process.memory_info()
    total_memory = psutil.virtual_memory().total
    return (memory_info.rss / total_memory) * 100
```

#### Parallel Processing
- Use `ProcessPoolExecutor` for CPU-bound tasks
- Use `ThreadPoolExecutor` for I/O-bound tasks
- Implement proper resource cleanup
- Handle process failures gracefully

```python
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Callable, Any

def parallel_execute(
    func: Callable[[Any], Any],
    tasks: List[Any],
    max_workers: Optional[int] = None
) -> List[Any]:
    """Execute function in parallel across multiple processes.
    
    Args:
        func: Function to execute
        tasks: List of task arguments
        max_workers: Maximum number of worker processes
    
    Returns:
        List of results in original order
    """
    results = [None] * len(tasks)
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_index = {
            executor.submit(func, task): i 
            for i, task in enumerate(tasks)
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_index):
            index = future_to_index[future]
            try:
                results[index] = future.result()
            except Exception as e:
                logger.error(f"Task {index} failed: {str(e)}")
                results[index] = None
    
    return results
```

### Testing Standards

#### Unit Testing
- Use `pytest` for all testing
- Aim for >90% code coverage
- Test both success and failure cases
- Use fixtures for common test data

```python
import pytest
import pandas as pd
from unittest.mock import Mock, patch
from src.strategies.base_strategy import BaseStrategy

@pytest.fixture
def sample_data() -> pd.DataFrame:
    """Provide sample trading data for tests."""
    return pd.DataFrame({
        'timestamp': pd.date_range('2023-01-01', periods=100, freq='1H'),
        'open': range(100, 200),
        'high': range(105, 205),
        'low': range(95, 195),
        'close': range(102, 202),
        'volume': range(1000, 1100)
    })

class TestBaseStrategy:
    """Test cases for BaseStrategy class."""
    
    def test_strategy_initialization(self, sample_data):
        """Test strategy initializes correctly with valid data."""
        strategy = BaseStrategy(data=sample_data)
        assert strategy.data is not None
        assert len(strategy.data) == 100
    
    def test_strategy_invalid_data(self):
        """Test strategy raises error with invalid data."""
        with pytest.raises(DataValidationError):
            BaseStrategy(data=pd.DataFrame())
    
    @patch('src.strategies.base_strategy.logger')
    def test_strategy_logging(self, mock_logger, sample_data):
        """Test strategy logs initialization."""
        strategy = BaseStrategy(data=sample_data)
        mock_logger.info.assert_called_once()
```

#### Integration Testing
- Test complete workflows end-to-end
- Use realistic data sizes
- Test parallel processing functionality
- Validate performance requirements

### Configuration Management

#### YAML Configuration Files
- Use YAML for all configuration
- Validate configuration on startup
- Support environment-specific overrides
- Document all configuration options

```yaml
# config/development.yaml
backtesting:
  engine: "backtrader"
  initial_cash: 100000
  commission: 0.001
  
optimization:
  max_workers: 8
  chunk_size: 1000
  memory_limit_gb: 16
  
data:
  source: "ccxt"
  cache_dir: "./data/cache"
  storage_format: "hdf5"
  
logging:
  level: "DEBUG"
  file: "./logs/backtester.log"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

### File Organization

#### Directory Structure
```
Backtester4/
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   ├── fetcher.py
│   │   ├── preprocessor.py
│   │   └── storage.py
│   ├── strategies/
│   │   ├── __init__.py
│   │   ├── base_strategy.py
│   │   └── implementations/
│   ├── optimization/
│   │   ├── __init__.py
│   │   ├── parallel_optimizer.py
│   │   └── algorithms/
│   ├── engine/
│   │   ├── __init__.py
│   │   └── backtest_engine.py
│   └── utils/
│       ├── __init__.py
│       ├── config.py
│       └── logging.py
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── config/
│   ├── development.yaml
│   ├── production.yaml
│   └── strategy_params.yaml
├── data/
│   ├── raw/
│   ├── processed/
│   └── cache/
├── logs/
├── docs/
├── scripts/
├── requirements.txt
├── setup.py
└── README.md
```

### Code Review Guidelines

#### Review Checklist
- [ ] Code follows PEP 8 and style guidelines
- [ ] All functions have type hints and docstrings
- [ ] Error handling is comprehensive
- [ ] Tests cover new functionality
- [ ] Performance implications considered
- [ ] Memory usage optimized
- [ ] Logging is appropriate
- [ ] Configuration is externalized
- [ ] Documentation is updated

#### Performance Review
- [ ] Algorithm complexity is optimal
- [ ] Memory usage is efficient
- [ ] Parallel processing is utilized appropriately
- [ ] I/O operations are minimized
- [ ] Caching is implemented where beneficial

This style guide ensures consistent, maintainable, and high-performance code across the entire backtesting system while leveraging the full capabilities of the available hardware.