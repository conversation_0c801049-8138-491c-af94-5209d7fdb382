# Multi-stage Dockerfile for Crypto Backtesting System
# Optimized for Ryzen 9 7950X + RTX 5060Ti + 96GB DDR5

# Build stage
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILDPLATFORM
ARG TARGETPLATFORM
ARG BUILDARCH
ARG TARGETARCH

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    pkg-config \
    cmake \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install additional performance packages
RUN pip install --no-cache-dir \
    cython \
    numba \
    bottleneck \
    numexpr

# Production stage
FROM python:3.11-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libopenblas0 \
    liblapack3 \
    libgomp1 \
    curl \
    htop \
    redis-tools \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r backtester && useradd -r -g backtester backtester

# Set working directory
WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY setup.py .
COPY README.md .
COPY STYLE_GUIDE.md .
COPY AI_BEHAVIOR_RULES.md .

# Install the application
RUN pip install -e .

# Create necessary directories
RUN mkdir -p \
    /app/data/raw \
    /app/data/processed \
    /app/data/cache \
    /app/data/results \
    /app/logs \
    /app/tests/data \
    && chown -R backtester:backtester /app

# Set environment variables
ENV PYTHONPATH=/app/src
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV NUMBA_CACHE_DIR=/app/.numba_cache
ENV OMP_NUM_THREADS=16
ENV OPENBLAS_NUM_THREADS=16
ENV MKL_NUM_THREADS=16

# Performance optimizations
ENV PYTHONHASHSEED=0
ENV MALLOC_ARENA_MAX=4
ENV MALLOC_MMAP_THRESHOLD_=131072
ENV MALLOC_TRIM_THRESHOLD_=131072
ENV MALLOC_TOP_PAD_=131072
ENV MALLOC_MMAP_MAX_=65536

# Switch to non-root user
USER backtester

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import src; print('OK')" || exit 1

# Expose ports
EXPOSE 8080 8081

# Default command
CMD ["python", "-m", "src.cli", "--help"]

# Development stage
FROM production as development

# Switch back to root for development tools
USER root

# Install development dependencies
RUN apt-get update && apt-get install -y \
    vim \
    git \
    tmux \
    tree \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages
RUN pip install --no-cache-dir \
    jupyter \
    ipython \
    pytest-xdist \
    pytest-benchmark \
    memory-profiler \
    line-profiler \
    py-spy \
    black \
    isort \
    flake8 \
    mypy

# Copy development files
COPY tests/ ./tests/
COPY .gitignore .
COPY pyproject.toml .

# Set development environment variables
ENV ENVIRONMENT=development
ENV DEBUG=true
ENV LOG_LEVEL=DEBUG

# Switch back to backtester user
USER backtester

# Development command
CMD ["python", "-m", "src.cli", "monitor", "--dashboard"]

# GPU-enabled stage (for CUDA support)
FROM nvidia/cuda:12.0-runtime-ubuntu22.04 as gpu

# Install Python 3.11
RUN apt-get update && apt-get install -y \
    software-properties-common \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y \
    python3.11 \
    python3.11-venv \
    python3.11-dev \
    python3-pip \
    libopenblas0 \
    liblapack3 \
    libgomp1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create symlinks
RUN ln -s /usr/bin/python3.11 /usr/bin/python
RUN ln -s /usr/bin/python3.11 /usr/bin/python3

# Create non-root user
RUN groupadd -r backtester && useradd -r -g backtester backtester

# Set working directory
WORKDIR /app

# Copy virtual environment and application from production stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install GPU-specific packages
RUN pip install --no-cache-dir \
    cupy-cuda12x \
    torch \
    tensorflow-gpu

# Copy application code
COPY --from=production /app .

# Set GPU environment variables
ENV CUDA_VISIBLE_DEVICES=0
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility
ENV NUMBA_ENABLE_CUDASIM=0
ENV NUMBA_CUDA_DRIVER=/usr/local/cuda/lib64/libcuda.so

# Performance optimizations for GPU
ENV CUDA_CACHE_PATH=/app/.cuda_cache
ENV CUPY_CACHE_DIR=/app/.cupy_cache

# Create cache directories
RUN mkdir -p /app/.cuda_cache /app/.cupy_cache /app/.numba_cache \
    && chown -R backtester:backtester /app

# Switch to non-root user
USER backtester

# GPU health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import cupy; print('GPU OK')" || exit 1

# Default GPU command
CMD ["python", "-m", "src.cli", "optimize", "--gpu"]

# Testing stage
FROM production as testing

# Switch to root for test dependencies
USER root

# Install test dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-cov \
    pytest-xdist \
    pytest-benchmark \
    pytest-mock \
    hypothesis \
    factory-boy

# Copy test files
COPY tests/ ./tests/
COPY pytest.ini .
COPY .coveragerc .

# Set test environment
ENV ENVIRONMENT=testing
ENV PYTHONPATH=/app/src:/app/tests

# Switch back to backtester user
USER backtester

# Test command
CMD ["python", "-m", "pytest", "tests/", "-v", "--cov=src", "--cov-report=html"]