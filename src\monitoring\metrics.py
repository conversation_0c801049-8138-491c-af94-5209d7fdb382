"""Monitoring and metrics module for cryptocurrency backtesting system.

This module provides comprehensive monitoring of system performance, resource usage,
and backtesting metrics with real-time tracking and alerting capabilities.
"""

import time
import threading
import psutil
import gc
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import json
import pickle
from pathlib import Path
import queue
import statistics
from contextlib import contextmanager

import pandas as pd
import numpy as np
from threading import Lock, Event

from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class MetricType(Enum):
    """Types of metrics."""
    COUNTER = "counter"  # Monotonically increasing
    GAUGE = "gauge"  # Current value
    HISTOGRAM = "histogram"  # Distribution of values
    TIMER = "timer"  # Duration measurements
    RATE = "rate"  # Rate of change


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricValue:
    """Individual metric value with timestamp."""
    value: Union[int, float]
    timestamp: datetime = field(default_factory=datetime.now)
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class MetricSummary:
    """Statistical summary of metric values."""
    count: int
    sum: float
    min: float
    max: float
    mean: float
    median: float
    std: float
    p95: float
    p99: float
    last_value: float
    last_timestamp: datetime


@dataclass
class Alert:
    """Alert definition and state."""
    name: str
    metric_name: str
    condition: str  # e.g., "> 0.8", "< 100", "== 0"
    level: AlertLevel
    message: str
    enabled: bool = True
    cooldown_seconds: int = 300  # 5 minutes
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0


@dataclass
class SystemMetrics:
    """System resource metrics."""
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_available_gb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    thread_count: int
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class BacktestMetrics:
    """Backtesting performance metrics."""
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    trades_per_day: float
    avg_trade_duration: float
    timestamp: datetime = field(default_factory=datetime.now)


class MetricCollector:
    """Collects and stores individual metrics."""
    
    def __init__(self, name: str, metric_type: MetricType, max_history: int = 10000):
        """Initialize metric collector.
        
        Args:
            name: Metric name
            metric_type: Type of metric
            max_history: Maximum number of values to keep
        """
        self.name = name
        self.metric_type = metric_type
        self.max_history = max_history
        
        # Storage
        self.values: deque = deque(maxlen=max_history)
        self.tags_index: Dict[str, List[int]] = defaultdict(list)
        
        # Statistics cache
        self._summary_cache: Optional[MetricSummary] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(seconds=30)
        
        # Threading
        self._lock = Lock()
    
    def record(self, value: Union[int, float], tags: Optional[Dict[str, str]] = None) -> None:
        """Record a metric value.
        
        Args:
            value: Metric value
            tags: Optional tags for the metric
        """
        tags = tags or {}
        metric_value = MetricValue(value=value, tags=tags)
        
        with self._lock:
            self.values.append(metric_value)
            
            # Update tags index
            index = len(self.values) - 1
            for tag_key, tag_value in tags.items():
                tag_string = f"{tag_key}:{tag_value}"
                self.tags_index[tag_string].append(index)
            
            # Invalidate cache
            self._summary_cache = None
    
    def increment(self, amount: Union[int, float] = 1, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment counter metric.
        
        Args:
            amount: Amount to increment
            tags: Optional tags
        """
        if self.metric_type != MetricType.COUNTER:
            raise ValueError(f"Increment only supported for COUNTER metrics, got {self.metric_type}")
        
        # Get last value
        last_value = 0
        if self.values:
            last_value = self.values[-1].value
        
        self.record(last_value + amount, tags)
    
    def set(self, value: Union[int, float], tags: Optional[Dict[str, str]] = None) -> None:
        """Set gauge metric value.
        
        Args:
            value: New value
            tags: Optional tags
        """
        if self.metric_type != MetricType.GAUGE:
            raise ValueError(f"Set only supported for GAUGE metrics, got {self.metric_type}")
        
        self.record(value, tags)
    
    def time_operation(self, tags: Optional[Dict[str, str]] = None):
        """Context manager for timing operations.
        
        Args:
            tags: Optional tags
            
        Returns:
            Context manager
        """
        if self.metric_type != MetricType.TIMER:
            raise ValueError(f"Time operation only supported for TIMER metrics, got {self.metric_type}")
        
        @contextmanager
        def timer():
            start_time = time.time()
            try:
                yield
            finally:
                duration = time.time() - start_time
                self.record(duration, tags)
        
        return timer()
    
    def get_summary(self, force_refresh: bool = False) -> Optional[MetricSummary]:
        """Get statistical summary of metric values.
        
        Args:
            force_refresh: Force cache refresh
            
        Returns:
            Metric summary or None if no data
        """
        with self._lock:
            # Check cache
            now = datetime.now()
            if (not force_refresh and 
                self._summary_cache is not None and 
                self._cache_timestamp is not None and 
                now - self._cache_timestamp < self._cache_ttl):
                return self._summary_cache
            
            if not self.values:
                return None
            
            # Calculate summary
            values = [v.value for v in self.values]
            
            summary = MetricSummary(
                count=len(values),
                sum=sum(values),
                min=min(values),
                max=max(values),
                mean=statistics.mean(values),
                median=statistics.median(values),
                std=statistics.stdev(values) if len(values) > 1 else 0.0,
                p95=np.percentile(values, 95) if len(values) > 1 else values[0],
                p99=np.percentile(values, 99) if len(values) > 1 else values[0],
                last_value=values[-1],
                last_timestamp=self.values[-1].timestamp
            )
            
            # Update cache
            self._summary_cache = summary
            self._cache_timestamp = now
            
            return summary
    
    def get_values(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        tags: Optional[Dict[str, str]] = None
    ) -> List[MetricValue]:
        """Get metric values with optional filtering.
        
        Args:
            start_time: Start time filter
            end_time: End time filter
            tags: Tag filters
            
        Returns:
            Filtered metric values
        """
        with self._lock:
            filtered_values = []
            
            for value in self.values:
                # Time filter
                if start_time and value.timestamp < start_time:
                    continue
                if end_time and value.timestamp > end_time:
                    continue
                
                # Tag filter
                if tags:
                    match = True
                    for tag_key, tag_value in tags.items():
                        if value.tags.get(tag_key) != tag_value:
                            match = False
                            break
                    if not match:
                        continue
                
                filtered_values.append(value)
            
            return filtered_values
    
    def clear(self) -> None:
        """Clear all metric values."""
        with self._lock:
            self.values.clear()
            self.tags_index.clear()
            self._summary_cache = None


class AlertManager:
    """Manages alerts and notifications."""
    
    def __init__(self):
        """Initialize alert manager."""
        self.logger = get_logger("alert_manager")
        
        # Alert storage
        self.alerts: Dict[str, Alert] = {}
        self.alert_history: List[Tuple[Alert, datetime, float]] = []
        
        # Callbacks
        self.alert_callbacks: Dict[AlertLevel, List[Callable]] = {
            level: [] for level in AlertLevel
        }
        
        # Threading
        self._lock = Lock()
    
    def add_alert(
        self,
        name: str,
        metric_name: str,
        condition: str,
        level: AlertLevel,
        message: str,
        cooldown_seconds: int = 300
    ) -> None:
        """Add alert definition.
        
        Args:
            name: Alert name
            metric_name: Metric to monitor
            condition: Alert condition (e.g., "> 0.8")
            level: Alert level
            message: Alert message
            cooldown_seconds: Cooldown period
        """
        alert = Alert(
            name=name,
            metric_name=metric_name,
            condition=condition,
            level=level,
            message=message,
            cooldown_seconds=cooldown_seconds
        )
        
        with self._lock:
            self.alerts[name] = alert
        
        self.logger.info(f"Added alert: {name} for metric {metric_name}")
    
    def remove_alert(self, name: str) -> bool:
        """Remove alert definition.
        
        Args:
            name: Alert name
            
        Returns:
            True if alert was removed
        """
        with self._lock:
            removed = self.alerts.pop(name, None) is not None
        
        if removed:
            self.logger.info(f"Removed alert: {name}")
        
        return removed
    
    def check_alerts(self, metric_name: str, value: float) -> List[Alert]:
        """Check if any alerts should be triggered.
        
        Args:
            metric_name: Metric name
            value: Current metric value
            
        Returns:
            List of triggered alerts
        """
        triggered_alerts = []
        now = datetime.now()
        
        with self._lock:
            for alert in self.alerts.values():
                if not alert.enabled or alert.metric_name != metric_name:
                    continue
                
                # Check cooldown
                if (alert.last_triggered and 
                    (now - alert.last_triggered).total_seconds() < alert.cooldown_seconds):
                    continue
                
                # Evaluate condition
                if self._evaluate_condition(value, alert.condition):
                    alert.last_triggered = now
                    alert.trigger_count += 1
                    triggered_alerts.append(alert)
                    
                    # Add to history
                    self.alert_history.append((alert, now, value))
                    
                    # Trigger callbacks
                    self._trigger_callbacks(alert, value)
        
        return triggered_alerts
    
    def _evaluate_condition(self, value: float, condition: str) -> bool:
        """Evaluate alert condition.
        
        Args:
            value: Current value
            condition: Condition string
            
        Returns:
            True if condition is met
        """
        try:
            # Simple condition evaluation
            # Format: "operator threshold" (e.g., "> 0.8", "< 100", "== 0")
            condition = condition.strip()
            
            if condition.startswith(">="):
                threshold = float(condition[2:].strip())
                return value >= threshold
            elif condition.startswith("<="):
                threshold = float(condition[2:].strip())
                return value <= threshold
            elif condition.startswith(">"): 
                threshold = float(condition[1:].strip())
                return value > threshold
            elif condition.startswith("<"):
                threshold = float(condition[1:].strip())
                return value < threshold
            elif condition.startswith("=="):
                threshold = float(condition[2:].strip())
                return abs(value - threshold) < 1e-9
            elif condition.startswith("!="):
                threshold = float(condition[2:].strip())
                return abs(value - threshold) >= 1e-9
            else:
                self.logger.error(f"Invalid condition format: {condition}")
                return False
        
        except Exception as e:
            self.logger.error(f"Error evaluating condition '{condition}': {e}")
            return False
    
    def _trigger_callbacks(self, alert: Alert, value: float) -> None:
        """Trigger alert callbacks.
        
        Args:
            alert: Triggered alert
            value: Current value
        """
        callbacks = self.alert_callbacks.get(alert.level, [])
        
        for callback in callbacks:
            try:
                callback(alert, value)
            except Exception as e:
                self.logger.error(f"Alert callback error: {e}")
    
    def add_callback(self, level: AlertLevel, callback: Callable[[Alert, float], None]) -> None:
        """Add alert callback.
        
        Args:
            level: Alert level
            callback: Callback function
        """
        self.alert_callbacks[level].append(callback)
    
    def get_alert_history(self, limit: int = 100) -> List[Tuple[Alert, datetime, float]]:
        """Get alert history.
        
        Args:
            limit: Maximum number of alerts to return
            
        Returns:
            List of (alert, timestamp, value) tuples
        """
        with self._lock:
            return self.alert_history[-limit:]


class SystemMonitor:
    """Monitors system resource usage."""
    
    def __init__(self, collection_interval: float = 1.0):
        """Initialize system monitor.
        
        Args:
            collection_interval: Collection interval in seconds
        """
        self.collection_interval = collection_interval
        self.logger = get_logger("system_monitor")
        
        # Monitoring state
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = Event()
        
        # Metrics
        self.metrics: Dict[str, MetricCollector] = {
            'cpu_percent': MetricCollector('cpu_percent', MetricType.GAUGE),
            'memory_percent': MetricCollector('memory_percent', MetricType.GAUGE),
            'memory_used_gb': MetricCollector('memory_used_gb', MetricType.GAUGE),
            'disk_usage_percent': MetricCollector('disk_usage_percent', MetricType.GAUGE),
            'network_bytes_sent': MetricCollector('network_bytes_sent', MetricType.COUNTER),
            'network_bytes_recv': MetricCollector('network_bytes_recv', MetricType.COUNTER),
            'process_count': MetricCollector('process_count', MetricType.GAUGE),
            'thread_count': MetricCollector('thread_count', MetricType.GAUGE)
        }
        
        # Baseline measurements
        self._baseline_network: Optional[Tuple[int, int]] = None
    
    def start_monitoring(self) -> None:
        """Start system monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("System monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop system monitoring."""
        if not self._monitoring:
            return
        
        self._monitoring = False
        self._stop_event.set()
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("System monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while not self._stop_event.wait(self.collection_interval):
            try:
                self._collect_metrics()
            except Exception as e:
                self.logger.error(f"System monitoring error: {e}")
    
    def _collect_metrics(self) -> None:
        """Collect system metrics."""
        # CPU usage
        cpu_percent = psutil.cpu_percent()
        self.metrics['cpu_percent'].set(cpu_percent)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / (1024**3)
        
        self.metrics['memory_percent'].set(memory_percent)
        self.metrics['memory_used_gb'].set(memory_used_gb)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        self.metrics['disk_usage_percent'].set(disk_usage_percent)
        
        # Network I/O
        network = psutil.net_io_counters()
        if self._baseline_network is None:
            self._baseline_network = (network.bytes_sent, network.bytes_recv)
        
        bytes_sent = network.bytes_sent - self._baseline_network[0]
        bytes_recv = network.bytes_recv - self._baseline_network[1]
        
        self.metrics['network_bytes_sent'].set(bytes_sent)
        self.metrics['network_bytes_recv'].set(bytes_recv)
        
        # Process and thread counts
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        self.metrics['process_count'].set(process_count)
        self.metrics['thread_count'].set(thread_count)
    
    def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics.
        
        Returns:
            Current system metrics
        """
        # Force collection
        self._collect_metrics()
        
        # Get latest values
        cpu_percent = self.metrics['cpu_percent'].values[-1].value if self.metrics['cpu_percent'].values else 0.0
        memory_percent = self.metrics['memory_percent'].values[-1].value if self.metrics['memory_percent'].values else 0.0
        memory_used_gb = self.metrics['memory_used_gb'].values[-1].value if self.metrics['memory_used_gb'].values else 0.0
        
        # Calculate additional metrics
        memory = psutil.virtual_memory()
        memory_available_gb = memory.available / (1024**3)
        
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024**3)
        
        network = psutil.net_io_counters()
        network_bytes_sent = network.bytes_sent
        network_bytes_recv = network.bytes_recv
        
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_gb=memory_used_gb,
            memory_available_gb=memory_available_gb,
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=network_bytes_sent,
            network_bytes_recv=network_bytes_recv,
            process_count=process_count,
            thread_count=thread_count
        )


class MetricsManager:
    """Main metrics management system."""
    
    def __init__(self, enable_system_monitoring: bool = True):
        """Initialize metrics manager.
        
        Args:
            enable_system_monitoring: Whether to enable system monitoring
        """
        self.logger = get_logger("metrics_manager")
        
        # Components
        self.metrics: Dict[str, MetricCollector] = {}
        self.alert_manager = AlertManager()
        self.system_monitor = SystemMonitor() if enable_system_monitoring else None
        
        # Configuration
        self.enable_system_monitoring = enable_system_monitoring
        
        # Default alerts
        self._setup_default_alerts()
        
        # Start monitoring
        if self.system_monitor:
            self.system_monitor.start_monitoring()
        
        self.logger.info("Metrics manager initialized")
    
    def create_metric(
        self,
        name: str,
        metric_type: MetricType,
        max_history: int = 10000
    ) -> MetricCollector:
        """Create a new metric collector.
        
        Args:
            name: Metric name
            metric_type: Type of metric
            max_history: Maximum history size
            
        Returns:
            Metric collector
        """
        if name in self.metrics:
            self.logger.warning(f"Metric {name} already exists")
            return self.metrics[name]
        
        metric = MetricCollector(name, metric_type, max_history)
        self.metrics[name] = metric
        
        self.logger.debug(f"Created metric: {name} ({metric_type.value})")
        return metric
    
    def get_metric(self, name: str) -> Optional[MetricCollector]:
        """Get metric collector by name.
        
        Args:
            name: Metric name
            
        Returns:
            Metric collector or None
        """
        return self.metrics.get(name)
    
    def record_metric(
        self,
        name: str,
        value: Union[int, float],
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a metric value.
        
        Args:
            name: Metric name
            value: Metric value
            tags: Optional tags
        """
        metric = self.metrics.get(name)
        if metric is None:
            self.logger.warning(f"Metric {name} not found, creating as GAUGE")
            metric = self.create_metric(name, MetricType.GAUGE)
        
        metric.record(value, tags)
        
        # Check alerts
        triggered_alerts = self.alert_manager.check_alerts(name, value)
        for alert in triggered_alerts:
            self.logger.warning(f"Alert triggered: {alert.name} - {alert.message}")
    
    def increment_counter(
        self,
        name: str,
        amount: Union[int, float] = 1,
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Increment a counter metric.
        
        Args:
            name: Metric name
            amount: Amount to increment
            tags: Optional tags
        """
        metric = self.metrics.get(name)
        if metric is None:
            metric = self.create_metric(name, MetricType.COUNTER)
        
        metric.increment(amount, tags)
    
    def set_gauge(
        self,
        name: str,
        value: Union[int, float],
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Set a gauge metric value.
        
        Args:
            name: Metric name
            value: New value
            tags: Optional tags
        """
        metric = self.metrics.get(name)
        if metric is None:
            metric = self.create_metric(name, MetricType.GAUGE)
        
        metric.set(value, tags)
        
        # Check alerts
        triggered_alerts = self.alert_manager.check_alerts(name, value)
        for alert in triggered_alerts:
            self.logger.warning(f"Alert triggered: {alert.name} - {alert.message}")
    
    def time_operation(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Time an operation.
        
        Args:
            name: Metric name
            tags: Optional tags
            
        Returns:
            Context manager
        """
        metric = self.metrics.get(name)
        if metric is None:
            metric = self.create_metric(name, MetricType.TIMER)
        
        return metric.time_operation(tags)
    
    def get_all_summaries(self) -> Dict[str, MetricSummary]:
        """Get summaries for all metrics.
        
        Returns:
            Dictionary of metric summaries
        """
        summaries = {}
        
        for name, metric in self.metrics.items():
            summary = metric.get_summary()
            if summary:
                summaries[name] = summary
        
        # Add system metrics if available
        if self.system_monitor:
            for name, metric in self.system_monitor.metrics.items():
                summary = metric.get_summary()
                if summary:
                    summaries[f"system.{name}"] = summary
        
        return summaries
    
    def export_metrics(self, format: str = "json") -> str:
        """Export metrics in specified format.
        
        Args:
            format: Export format ('json', 'prometheus')
            
        Returns:
            Exported metrics string
        """
        if format == "json":
            return self._export_json()
        elif format == "prometheus":
            return self._export_prometheus()
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_json(self) -> str:
        """Export metrics as JSON."""
        data = {
            'timestamp': datetime.now().isoformat(),
            'metrics': {}
        }
        
        summaries = self.get_all_summaries()
        for name, summary in summaries.items():
            data['metrics'][name] = {
                'count': summary.count,
                'sum': summary.sum,
                'min': summary.min,
                'max': summary.max,
                'mean': summary.mean,
                'median': summary.median,
                'std': summary.std,
                'p95': summary.p95,
                'p99': summary.p99,
                'last_value': summary.last_value,
                'last_timestamp': summary.last_timestamp.isoformat()
            }
        
        return json.dumps(data, indent=2)
    
    def _export_prometheus(self) -> str:
        """Export metrics in Prometheus format."""
        lines = []
        
        summaries = self.get_all_summaries()
        for name, summary in summaries.items():
            # Convert name to Prometheus format
            prom_name = name.replace('.', '_').replace('-', '_')
            
            lines.append(f"# HELP {prom_name} {name} metric")
            lines.append(f"# TYPE {prom_name} gauge")
            lines.append(f"{prom_name} {summary.last_value}")
            lines.append(f"{prom_name}_count {summary.count}")
            lines.append(f"{prom_name}_sum {summary.sum}")
            lines.append(f"{prom_name}_min {summary.min}")
            lines.append(f"{prom_name}_max {summary.max}")
            lines.append("")
        
        return "\n".join(lines)
    
    def _setup_default_alerts(self) -> None:
        """Setup default system alerts."""
        # CPU usage alert
        self.alert_manager.add_alert(
            name="high_cpu_usage",
            metric_name="system.cpu_percent",
            condition="> 80",
            level=AlertLevel.WARNING,
            message="High CPU usage detected"
        )
        
        # Memory usage alert
        self.alert_manager.add_alert(
            name="high_memory_usage",
            metric_name="system.memory_percent",
            condition="> 85",
            level=AlertLevel.WARNING,
            message="High memory usage detected"
        )
        
        # Critical memory alert
        self.alert_manager.add_alert(
            name="critical_memory_usage",
            metric_name="system.memory_percent",
            condition="> 95",
            level=AlertLevel.CRITICAL,
            message="Critical memory usage detected"
        )
        
        # Disk usage alert
        self.alert_manager.add_alert(
            name="high_disk_usage",
            metric_name="system.disk_usage_percent",
            condition="> 90",
            level=AlertLevel.WARNING,
            message="High disk usage detected"
        )
    
    def shutdown(self) -> None:
        """Shutdown metrics manager."""
        if self.system_monitor:
            self.system_monitor.stop_monitoring()
        
        self.logger.info("Metrics manager shutdown")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


# Global metrics manager instance
_global_metrics_manager: Optional[MetricsManager] = None


def get_metrics_manager() -> MetricsManager:
    """Get global metrics manager instance."""
    global _global_metrics_manager
    
    if _global_metrics_manager is None:
        _global_metrics_manager = MetricsManager()
    
    return _global_metrics_manager


def record_metric(name: str, value: Union[int, float], tags: Optional[Dict[str, str]] = None) -> None:
    """Record a metric value using global manager."""
    manager = get_metrics_manager()
    manager.record_metric(name, value, tags)


def increment_counter(name: str, amount: Union[int, float] = 1, tags: Optional[Dict[str, str]] = None) -> None:
    """Increment a counter using global manager."""
    manager = get_metrics_manager()
    manager.increment_counter(name, amount, tags)


def set_gauge(name: str, value: Union[int, float], tags: Optional[Dict[str, str]] = None) -> None:
    """Set a gauge value using global manager."""
    manager = get_metrics_manager()
    manager.set_gauge(name, value, tags)


def time_operation(name: str, tags: Optional[Dict[str, str]] = None):
    """Time an operation using global manager."""
    manager = get_metrics_manager()
    return manager.time_operation(name, tags)


def get_system_metrics() -> SystemMetrics:
    """Get current system metrics."""
    manager = get_metrics_manager()
    if manager.system_monitor:
        return manager.system_monitor.get_current_metrics()
    else:
        raise RuntimeError("System monitoring not enabled")