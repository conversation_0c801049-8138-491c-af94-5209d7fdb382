"""Performance analysis module for cryptocurrency backtesting system.

This module provides comprehensive performance analysis tools including
statistical metrics, risk analysis, visualization, and reporting capabilities.

Classes:
    PerformanceAnalyzer: Main performance analysis engine
    MetricsCalculator: Calculate trading performance metrics
    RiskAnalyzer: Risk analysis and VaR calculations
    DrawdownAnalyzer: Drawdown analysis and visualization
    ReportGenerator: Generate comprehensive performance reports
    Visualizer: Create performance charts and plots

Functions:
    analyze_performance: Comprehensive performance analysis
    calculate_metrics: Calculate standard performance metrics
    generate_report: Generate performance report
    create_charts: Create performance visualization charts

Example:
    >>> from src.analysis import PerformanceAnalyzer, ReportGenerator
    >>> 
    >>> # Analyze backtest results
    >>> analyzer = PerformanceAnalyzer()
    >>> metrics = analyzer.analyze(backtest_results)
    >>> 
    >>> # Print key metrics
    >>> print(f"Total Return: {metrics['total_return']:.2%}")
    >>> print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.4f}")
    >>> print(f"Max Drawdown: {metrics['max_drawdown']:.2%}")
    >>> 
    >>> # Generate comprehensive report
    >>> report_gen = ReportGenerator()
    >>> report = report_gen.generate_report(backtest_results)
    >>> report.save_html('performance_report.html')
"""

# Import analysis components
try:
    from .performance_analyzer import PerformanceAnalyzer
    from .metrics_calculator import MetricsCalculator
    from .risk_analyzer import RiskAnalyzer
    from .drawdown_analyzer import DrawdownAnalyzer
    from .report_generator import ReportGenerator
    from .visualizer import Visualizer
    from .benchmark import BenchmarkComparison
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Standard performance metrics
STANDARD_METRICS = [
    'total_return',
    'annual_return',
    'volatility',
    'sharpe_ratio',
    'sortino_ratio',
    'calmar_ratio',
    'max_drawdown',
    'avg_drawdown',
    'drawdown_duration',
    'win_rate',
    'loss_rate',
    'avg_win',
    'avg_loss',
    'profit_factor',
    'payoff_ratio',
    'total_trades',
    'winning_trades',
    'losing_trades'
]

# Risk metrics
RISK_METRICS = [
    'var_95',           # Value at Risk (95%)
    'var_99',           # Value at Risk (99%)
    'cvar_95',          # Conditional VaR (95%)
    'cvar_99',          # Conditional VaR (99%)
    'skewness',         # Return skewness
    'kurtosis',         # Return kurtosis
    'beta',             # Market beta
    'alpha',            # Jensen's alpha
    'tracking_error',   # Tracking error vs benchmark
    'information_ratio', # Information ratio
    'treynor_ratio',    # Treynor ratio
    'ulcer_index',      # Ulcer index
    'pain_index'        # Pain index
]

# Advanced metrics
ADVANCED_METRICS = [
    'omega_ratio',      # Omega ratio
    'kappa_3',          # Kappa 3 ratio
    'gain_loss_ratio',  # Gain-to-loss ratio
    'expectancy',       # Mathematical expectancy
    'kelly_criterion',  # Kelly criterion percentage
    'mar_ratio',        # MAR ratio
    'sterling_ratio',   # Sterling ratio
    'burke_ratio',      # Burke ratio
    'tail_ratio',       # Tail ratio
    'common_sense_ratio' # Common sense ratio
]

# Benchmark symbols for comparison
BENCHMARK_SYMBOLS = {
    'btc': 'BTC/USDT',
    'eth': 'ETH/USDT',
    'market_cap': 'TOTAL/USDT',  # Total market cap
    'defi': 'DEFI/USDT',         # DeFi index
    'spy': 'SPY',                # S&P 500 ETF
    'qqq': 'QQQ',                # NASDAQ ETF
    'gold': 'GLD',               # Gold ETF
    'bonds': 'TLT'               # Treasury bonds ETF
}

# Chart types for visualization
CHART_TYPES = {
    'equity_curve': 'Portfolio equity curve',
    'drawdown': 'Drawdown chart',
    'returns_distribution': 'Returns distribution histogram',
    'rolling_sharpe': 'Rolling Sharpe ratio',
    'rolling_volatility': 'Rolling volatility',
    'monthly_returns': 'Monthly returns heatmap',
    'trade_analysis': 'Trade analysis scatter plot',
    'correlation_matrix': 'Asset correlation matrix',
    'risk_return': 'Risk-return scatter plot',
    'underwater': 'Underwater plot'
}

# Report sections
REPORT_SECTIONS = [
    'executive_summary',
    'performance_overview',
    'risk_analysis',
    'trade_analysis',
    'drawdown_analysis',
    'benchmark_comparison',
    'monthly_performance',
    'rolling_metrics',
    'distribution_analysis',
    'recommendations'
]

# Performance thresholds for rating
PERFORMANCE_RATINGS = {
    'excellent': {
        'sharpe_ratio': 2.0,
        'annual_return': 0.3,
        'max_drawdown': 0.1,
        'win_rate': 0.6
    },
    'good': {
        'sharpe_ratio': 1.5,
        'annual_return': 0.2,
        'max_drawdown': 0.15,
        'win_rate': 0.5
    },
    'acceptable': {
        'sharpe_ratio': 1.0,
        'annual_return': 0.1,
        'max_drawdown': 0.2,
        'win_rate': 0.4
    },
    'poor': {
        'sharpe_ratio': 0.5,
        'annual_return': 0.05,
        'max_drawdown': 0.3,
        'win_rate': 0.3
    }
}

def analyze_performance(results, benchmark=None, risk_free_rate=0.02, **kwargs):
    """Comprehensive performance analysis.
    
    Args:
        results: Backtest results to analyze
        benchmark: Benchmark data for comparison
        risk_free_rate: Risk-free rate for Sharpe calculation
        **kwargs: Additional analysis parameters
        
    Returns:
        dict: Comprehensive performance metrics
    """
    # This would be implemented when the actual analysis classes are created
    raise NotImplementedError("analyze_performance will be implemented with analysis classes")

def calculate_metrics(returns, benchmark_returns=None, risk_free_rate=0.02):
    """Calculate standard performance metrics.
    
    Args:
        returns: Strategy returns series
        benchmark_returns: Benchmark returns for comparison
        risk_free_rate: Risk-free rate for calculations
        
    Returns:
        dict: Performance metrics dictionary
    """
    # This would be implemented when the actual metrics classes are created
    raise NotImplementedError("calculate_metrics will be implemented with metrics classes")

# Export public API
__all__ = [
    "PerformanceAnalyzer",
    "MetricsCalculator",
    "RiskAnalyzer",
    "DrawdownAnalyzer",
    "ReportGenerator",
    "Visualizer",
    "BenchmarkComparison",
    "analyze_performance",
    "calculate_metrics",
    "STANDARD_METRICS",
    "RISK_METRICS",
    "ADVANCED_METRICS",
    "BENCHMARK_SYMBOLS",
    "CHART_TYPES",
    "REPORT_SECTIONS",
    "PERFORMANCE_RATINGS",
]