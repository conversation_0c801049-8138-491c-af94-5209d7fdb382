"""Crypto Backtesting System - High-Performance Trading Strategy Optimization.

A comprehensive backtesting system designed for cryptocurrency trading strategies
with parallel processing capabilities and hardware optimization.

This package provides:
- Parallel strategy optimization across multiple CPU cores
- Memory-efficient data processing for large datasets
- Extensible strategy framework with multiple implementations
- Advanced performance monitoring and analysis
- Hardware-optimized execution for high-end systems

Modules:
    data: Data fetching, preprocessing, and storage
    strategies: Trading strategy implementations and framework
    optimization: Parameter optimization and alpha discovery
    engine: Core backtesting engine and execution
    analysis: Performance metrics and result analysis
    parallel: Multi-process execution and coordination
    memory: Memory management and optimization
    monitoring: System monitoring and performance tracking
    utils: Utility functions and configuration management
    cli: Command-line interface tools

Example:
    >>> from src.strategies.ma_crossover import MovingAverageCrossover
    >>> from src.optimization.parallel_optimizer import ParallelOptimizer
    >>> from src.data.fetcher import CryptoDataFetcher
    >>> 
    >>> # Fetch data
    >>> fetcher = CryptoDataFetcher()
    >>> data = fetcher.fetch_data('BTC/USDT', '1h', '2023-01-01', '2023-12-31')
    >>> 
    >>> # Define optimization parameters
    >>> param_grid = {
    >>>     'fast_period': [10, 20, 30],
    >>>     'slow_period': [50, 100, 200]
    >>> }
    >>> 
    >>> # Run parallel optimization
    >>> optimizer = ParallelOptimizer(max_workers=16)
    >>> results = optimizer.optimize(MovingAverageCrossover, data, param_grid)
    >>> 
    >>> # Get best parameters
    >>> best_params = results.get_best_parameters()
    >>> print(f"Best parameters: {best_params}")

Hardware Requirements:
    - Multi-core CPU (16+ cores recommended)
    - 32GB+ RAM (64GB+ for large datasets)
    - SSD storage for optimal I/O performance
    - Stable internet connection for data fetching

Optimized For:
    - AMD Ryzen 9 7950X (32 threads)
    - 96GB DDR5 RAM
    - High-frequency trading strategy development

Author: Crypto Backtesting Team
Version: 0.1.0
License: MIT
"""

__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Package metadata
__title__ = "crypto-backtester"
__description__ = "High-performance cryptocurrency backtesting system with parallel optimization"
__url__ = "https://github.com/your-username/crypto-backtester"

# Version info
__version_info__ = tuple(map(int, __version__.split(".")))

# Import main components for easy access
try:
    from .utils.config import Config
    from .utils.logging import setup_logging
except ImportError:
    # Handle case where dependencies aren't installed yet
    pass

# Package-level constants
DEFAULT_CONFIG_PATH = "config/development.yaml"
DEFAULT_DATA_PATH = "data"
DEFAULT_LOG_PATH = "logs"

# Hardware optimization constants
DEFAULT_MAX_WORKERS = 16  # Conservative default
DEFAULT_MEMORY_LIMIT_GB = 32  # Conservative default
DEFAULT_CHUNK_SIZE = 10000  # Rows per chunk

# Supported exchanges and timeframes
SUPPORTED_EXCHANGES = [
    "binance", "coinbase", "kraken", "bitfinex", "huobi",
    "okx", "bybit", "kucoin", "gate", "mexc"
]

SUPPORTED_TIMEFRAMES = [
    "1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "1w"
]

# Performance monitoring thresholds
MEMORY_WARNING_THRESHOLD = 0.8  # 80% memory usage
CPU_WARNING_THRESHOLD = 0.95    # 95% CPU usage
DISK_WARNING_THRESHOLD = 0.9    # 90% disk usage

# Logging configuration
DEFAULT_LOG_LEVEL = "INFO"
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"

# Export public API
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "DEFAULT_CONFIG_PATH",
    "DEFAULT_DATA_PATH",
    "DEFAULT_LOG_PATH",
    "SUPPORTED_EXCHANGES",
    "SUPPORTED_TIMEFRAMES",
    "MEMORY_WARNING_THRESHOLD",
    "CPU_WARNING_THRESHOLD",
    "DISK_WARNING_THRESHOLD",
]