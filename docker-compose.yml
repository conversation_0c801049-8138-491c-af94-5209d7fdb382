# Docker Compose for Crypto Backtesting System
# Complete orchestration with Redis, PostgreSQL, and monitoring

version: '3.8'

services:
  # Main backtesting application
  backtester:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: crypto-backtester
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - CONFIG_FILE=/app/config/production.yaml
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - DB_USERNAME=backtester
      - DB_PASSWORD=secure_password_123
      - REDIS_PASSWORD=redis_password_123
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - backtester_cache:/app/.cache
    ports:
      - "8080:8080"  # Dashboard
      - "8081:8081"  # API
    depends_on:
      - redis
      - postgres
    networks:
      - backtester_network
    deploy:
      resources:
        limits:
          cpus: '15.0'  # Use most of Ryzen 9 7950X
          memory: 80G   # Use most of 96GB RAM
        reservations:
          cpus: '8.0'
          memory: 32G
    healthcheck:
      test: ["CMD", "python", "-c", "import src; print('OK')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # GPU-enabled backtester (alternative service)
  backtester-gpu:
    build:
      context: .
      dockerfile: Dockerfile
      target: gpu
    container_name: crypto-backtester-gpu
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - CONFIG_FILE=/app/config/production.yaml
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - DB_USERNAME=backtester
      - DB_PASSWORD=secure_password_123
      - REDIS_PASSWORD=redis_password_123
      - CUDA_VISIBLE_DEVICES=0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - backtester_cache:/app/.cache
    ports:
      - "8082:8080"  # Alternative dashboard port
    depends_on:
      - redis
      - postgres
    networks:
      - backtester_network
    runtime: nvidia
    deploy:
      resources:
        limits:
          cpus: '15.0'
          memory: 80G
        reservations:
          cpus: '8.0'
          memory: 32G
    profiles:
      - gpu  # Only start with --profile gpu

  # Redis for caching and task queues
  redis:
    image: redis:7-alpine
    container_name: crypto-backtester-redis
    restart: unless-stopped
    command: >
      redis-server
      --requirepass redis_password_123
      --maxmemory 8gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - backtester_network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis_password_123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for results storage
  postgres:
    image: postgres:15-alpine
    container_name: crypto-backtester-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=crypto_backtester
      - POSTGRES_USER=backtester
      - POSTGRES_PASSWORD=secure_password_123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - backtester_network
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=2GB
      -c effective_cache_size=6GB
      -c work_mem=64MB
      -c maintenance_work_mem=512MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U backtester -d crypto_backtester"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: crypto-backtester-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password_123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=redis-datasource,postgres-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - backtester_network
    depends_on:
      - postgres
      - redis

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: crypto-backtester-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - backtester_network

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: crypto-backtester-node-exporter
    restart: unless-stopped
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - backtester_network

  # Jupyter notebook for analysis
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: crypto-backtester-jupyter
    restart: unless-stopped
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=jupyter_token_123
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data:ro
      - ./src:/app/src:ro
    ports:
      - "8888:8888"
    networks:
      - backtester_network
    command: >
      jupyter lab
      --ip=0.0.0.0
      --port=8888
      --no-browser
      --allow-root
      --notebook-dir=/app/notebooks
    profiles:
      - development

  # Worker nodes for distributed processing
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - CONFIG_FILE=/app/config/production.yaml
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - DB_USERNAME=backtester
      - DB_PASSWORD=secure_password_123
      - REDIS_PASSWORD=redis_password_123
      - WORKER_MODE=true
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      - redis
      - postgres
    networks:
      - backtester_network
    deploy:
      replicas: 4  # Scale based on workload
      resources:
        limits:
          cpus: '3.0'
          memory: 16G
        reservations:
          cpus: '1.0'
          memory: 4G
    command: ["python", "-m", "src.parallel.worker"]
    profiles:
      - distributed

# Networks
networks:
  backtester_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local
  backtester_cache:
    driver: local

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# docker-compose.dev.yml content would go here for development overrides