#!/usr/bin/env python3
"""Main entry point for the Cryptocurrency Backtesting System.

This module provides the primary interface for running the backtesting system,
including CLI access, web interface, and programmatic API.
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import core modules
from src.utils.config import ConfigManager, get_config
from src.utils import setup_logging
from src.utils.logging import get_logger
from src.cli.main import cli

# Version information
__version__ = "1.0.0"
__author__ = "Crypto Backtesting Team"
__description__ = "Advanced Cryptocurrency Backtesting System with Parallel Optimization"


def setup_environment() -> None:
    """Set up the application environment."""
    # Create necessary directories
    directories = [
        "data",
        "results",
        "logs",
        "cache",
        "strategies",
        "configs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # Set up logging
    setup_logging()
    
    logger = get_logger("main")
    logger.info(f"Starting Cryptocurrency Backtesting System v{__version__}")
    logger.info(f"Working directory: {Path.cwd()}")


def check_dependencies() -> bool:
    """Check if all required dependencies are available."""
    logger = get_logger("main")
    
    # Map package names to import names
    required_packages = {
        "pandas": "pandas",
        "numpy": "numpy",
        "backtrader": "backtrader",
        "ccxt": "ccxt",
        "redis": "redis",
        "sqlalchemy": "sqlalchemy",
        "psutil": "psutil",
        "rich": "rich",
        "click": "click",
        "loguru": "loguru",
        "pyyaml": "yaml",
        "h5py": "h5py",
        "tables": "tables"
    }
    
    optional_packages = {
        "ray": "Distributed computing support",
        "dask": "Parallel computing support",
        "joblib": "Parallel processing support",
        "optuna": "Advanced optimization algorithms",
        "scikit-optimize": "Bayesian optimization",
        "ta-lib": "Technical analysis indicators",
        "plotly": "Interactive plotting",
        "dash": "Web dashboard"
    }
    
    missing_required = []
    missing_optional = []
    
    # Check required packages
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_required.append(package_name)
    
    # Check optional packages
    for package, description in optional_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_optional.append((package, description))
    
    # Report missing packages
    if missing_required:
        logger.error(f"Missing required packages: {', '.join(missing_required)}")
        logger.error("Please install missing packages with: pip install -r requirements.txt")
        return False
    
    if missing_optional:
        logger.warning("Missing optional packages:")
        for package, description in missing_optional:
            logger.warning(f"  - {package}: {description}")
        logger.warning("Some features may not be available.")
    
    logger.info("All required dependencies are available")
    return True


def display_banner() -> None:
    """Display application banner."""
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Cryptocurrency Backtesting System                        ║
║                                                                              ║
║  Version: {__version__:<20} Author: {__author__:<30} ║
║                                                                              ║
║  Features:                                                                   ║
║  • Advanced backtesting engine with Backtrader integration                  ║
║  • Parallel parameter optimization (Grid, Random, Bayesian, Optuna)         ║
║  • Multi-exchange cryptocurrency data support                               ║
║  • Real-time performance monitoring and analysis                            ║
║  • Memory-efficient data processing and caching                             ║
║  • Distributed computing support (Ray, Dask)                                ║
║                                                                              ║
║  Usage: python main.py [COMMAND] [OPTIONS]                                  ║
║         python main.py --help                                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)


def main() -> None:
    """Main application entry point."""
    try:
        # Display banner
        display_banner()
        
        # Set up environment
        setup_environment()
        
        # Check dependencies
        if not check_dependencies():
            sys.exit(1)
        
        # Load configuration
        config = get_config()
        logger = get_logger("main")
        
        # Log system information
        import psutil
        logger.info(f"System: {psutil.cpu_count()} CPUs, {psutil.virtual_memory().total / (1024**3):.1f} GB RAM")
        
        # Start CLI
        cli()
        
    except KeyboardInterrupt:
        print("\n\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\nFatal error: {e}")
        if "--verbose" in sys.argv or "-v" in sys.argv:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()