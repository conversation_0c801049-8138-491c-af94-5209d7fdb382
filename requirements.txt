# Core dependencies
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Data fetching and storage
ccxt>=4.0.0
h5py>=3.8.0
redis>=4.5.0
psycopg2-binary>=2.9.0
pyarrow>=12.0.0
tables>=3.9.0  # PyTables for HDF5
sqlalchemy>=2.0.0

# Technical analysis
TA-Lib>=0.4.25
numba>=0.57.0
statsmodels>=0.14.0

# Optimization and parallel computing
optuna>=3.2.0
ray[default]>=2.5.0
dask[complete]>=2023.5.0
joblib>=1.2.0
scikit-optimize>=0.9.0

# Visualization and reporting
plotly>=5.14.0
matplotlib>=3.7.0
seaborn>=0.12.0
kaleido>=0.2.1

# CLI and user interface
rich>=13.3.0
click>=8.1.0
tqdm>=4.65.0

# Configuration and utilities
PyYAML>=6.0
pydantic>=2.0.0
python-dotenv>=1.0.0
typing-extensions>=4.5.0

# Logging and monitoring
structlog>=23.1.0
psutil>=5.9.0
prometheus-client>=0.16.0
memory-profiler>=0.61.0

# Plotting and Visualization
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
bokeh==3.3.2

# Testing Framework
pytest==7.4.3
pytest-cov==4.1.0
pytest-xdist==3.5.0  # Parallel test execution
pytest-benchmark==4.0.0
pytest-mock==3.12.0

# Code Quality and Formatting
black==23.12.0
isort==5.13.2
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==2.0.0
sphinx-autodoc-typehints==1.25.2

# Async and Concurrency
aiohttp==3.9.1
aiofiles==23.2.1
asyncio-throttle==1.0.2

# Data Validation
pydantic==2.5.2
cerberus==1.3.5

# Networking and APIs
requests==2.31.0
websockets==12.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
more-itertools==10.1.0
itertools-recipes==0.1.0

# Machine Learning (Optional)
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0

# GPU Acceleration (Optional)
# cupy-cuda12x==12.3.0  # Uncomment if CUDA 12.x is available
# cudf==23.12.0  # Uncomment for GPU-accelerated DataFrames

# Development Tools
ipython==8.18.1
jupyter==1.0.0
jupyterlab==4.0.9

# File Format Support
openpyxl==3.1.2
xlsxwriter==3.1.9
fastparquet==2023.10.1
pyarrow==14.0.2

# Cryptography and Security
cryptography==41.0.8

# Environment and System
platform-system==1.0.0
psutil==5.9.6

# Memory Management
gc-python-utils==1.0.0
objgraph==3.6.0

# Progress Bars and UI
rich-progress==1.0.0
spinners==0.0.24

# Error Handling and Retry
retrying==1.3.4
tenacity==8.2.3

# Time Series Analysis
arch==6.2.0
statsforecast==1.6.0

# Financial Calculations
quantlib==1.32
empyrical==0.5.5
pyfolio==0.9.2

# Configuration Validation
jsonschema==4.20.0
voluptuous==0.14.1

# Caching
diskcache==5.6.3
cachetools==5.3.2

# Serialization
pickle5==0.0.12
dill==0.3.7
joblib==1.3.2

# Process Management
supervisor==4.2.5

# Monitoring and Alerting
prometheus-client==0.19.0

# Docker Support
docker==6.1.3

# Development Dependencies
bump2version==1.0.1
twine==4.0.2
wheel==0.42.0
setuptools==69.0.2

# Type Checking
types-PyYAML==*********
types-requests==2.31.0.10
types-python-dateutil==2.8.19.14

# Linting Extensions
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.12.2

# Performance Profiling
line-profiler==4.1.1
snakeviz==2.2.0

# Data Quality
great-expectations==0.18.8
pandera==0.17.2

# Backup and Recovery
backup-utils==1.0.0

# System Information
platform==1.0.8
distro==1.8.0