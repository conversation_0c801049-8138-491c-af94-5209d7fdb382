"""Utility functions and configuration management for cryptocurrency backtesting system.

This module provides essential utility functions, configuration management,
logging setup, and common helper functions used throughout the system.

Classes:
    Config: Configuration management and validation
    Logger: Enhanced logging with performance tracking
    DataValidator: Data validation utilities
    FileManager: File and directory management
    TimeUtils: Time and date utilities for trading
    MathUtils: Mathematical and statistical utilities

Functions:
    setup_logging: Configure system logging
    load_config: Load configuration from file
    validate_data: Validate input data
    format_currency: Format currency values
    calculate_returns: Calculate returns from price data

Example:
    >>> from src.utils import Config, setup_logging, TimeUtils
    >>> 
    >>> # Setup logging
    >>> setup_logging(level='INFO', log_file='backtester.log')
    >>> 
    >>> # Load configuration
    >>> config = Config.load('config/development.yaml')
    >>> print(f"Max workers: {config.get('parallel.max_workers')}")
    >>> 
    >>> # Time utilities
    >>> market_hours = TimeUtils.get_market_hours('crypto')
    >>> is_trading_time = TimeUtils.is_market_open('crypto')
"""

# Import utility components
try:
    from .config import Config
    from .logging import Logger, setup_logging
    from .validators import DataValidator
    from .file_manager import FileManager
    from .time_utils import TimeUtils
    from .math_utils import MathUtils
    from .formatters import CurrencyFormatter, PercentFormatter
    from .decorators import timing, retry, cache
except ImportError:
    # Handle case where modules aren't created yet
    pass

# Module metadata
__version__ = "0.1.0"
__author__ = "Crypto Backtesting Team"

# Configuration file paths
CONFIG_PATHS = {
    'development': 'config/development.yaml',
    'production': 'config/production.yaml',
    'testing': 'config/testing.yaml',
    'local': 'config/local.yaml'
}

# Default logging configuration
DEFAULT_LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s'
        },
        'performance': {
            'format': '%(asctime)s - PERF - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': 'logs/backtester.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'performance': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'performance',
            'filename': 'logs/performance.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 3
        }
    },
    'loggers': {
        'src': {
            'level': 'DEBUG',
            'handlers': ['console', 'file'],
            'propagate': False
        },
        'performance': {
            'level': 'INFO',
            'handlers': ['performance'],
            'propagate': False
        }
    },
    'root': {
        'level': 'WARNING',
        'handlers': ['console']
    }
}

# Data validation rules
VALIDATION_RULES = {
    'price_data': {
        'required_columns': ['open', 'high', 'low', 'close', 'volume'],
        'min_rows': 100,
        'max_missing_ratio': 0.05,
        'price_range': (0.0001, 1000000),
        'volume_range': (0, float('inf'))
    },
    'strategy_params': {
        'numeric_ranges': {
            'period': (1, 1000),
            'threshold': (0, 1),
            'multiplier': (0.1, 10)
        },
        'required_types': {
            'period': int,
            'threshold': float,
            'enabled': bool
        }
    }
}

# File management settings
FILE_SETTINGS = {
    'data_formats': ['.csv', '.parquet', '.h5', '.json'],
    'config_formats': ['.yaml', '.yml', '.json'],
    'backup_enabled': True,
    'compression': 'gzip',
    'max_file_size': 1073741824,  # 1GB
    'cleanup_days': 30
}

# Time zone settings
TIME_ZONES = {
    'utc': 'UTC',
    'ny': 'America/New_York',
    'london': 'Europe/London',
    'tokyo': 'Asia/Tokyo',
    'sydney': 'Australia/Sydney'
}

# Market hours (24/7 for crypto)
MARKET_HOURS = {
    'crypto': {
        'open': '00:00',
        'close': '23:59',
        'timezone': 'UTC',
        'days': [0, 1, 2, 3, 4, 5, 6]  # All days
    },
    'forex': {
        'open': '17:00',  # Sunday 5 PM EST
        'close': '17:00',  # Friday 5 PM EST
        'timezone': 'America/New_York',
        'days': [0, 1, 2, 3, 4]  # Monday to Friday
    }
}

# Mathematical constants
MATH_CONSTANTS = {
    'trading_days_per_year': 365,  # Crypto trades 365 days
    'hours_per_day': 24,
    'minutes_per_hour': 60,
    'seconds_per_minute': 60,
    'basis_points': 10000,
    'percentage': 100
}

# Performance thresholds
PERFORMANCE_THRESHOLDS = {
    'memory_warning': 0.8,      # 80% memory usage
    'cpu_warning': 0.9,         # 90% CPU usage
    'disk_warning': 0.85,       # 85% disk usage
    'response_time_warning': 5,  # 5 seconds
    'error_rate_warning': 0.05   # 5% error rate
}

def setup_logging(level='INFO', log_file=None, config_file=None):
    """Setup system logging configuration.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
        config_file: Optional logging config file
    """
    import logging
    import sys
    from pathlib import Path
    
    # Create logs directory if it doesn't exist
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    # Configure basic logging
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file or log_dir / 'backtester.log')
        ]
    )
    
    # Set up loguru for enhanced logging
    try:
        from loguru import logger
        logger.add(log_file or log_dir / 'backtester.log', rotation='10 MB')
    except ImportError:
        pass
    
    return logging.getLogger('backtester')

def load_config(config_path=None, environment='development'):
    """Load configuration from file.
    
    Args:
        config_path: Path to configuration file
        environment: Environment name (development, production, testing)
        
    Returns:
        Config: Configuration object
    """
    # This would be implemented when the actual config classes are created
    raise NotImplementedError("load_config will be implemented with config classes")

def validate_data(data, rules=None, strict=True):
    """Validate input data against rules.
    
    Args:
        data: Data to validate
        rules: Validation rules dictionary
        strict: Whether to raise exceptions on validation errors
        
    Returns:
        bool: True if data is valid
        
    Raises:
        ValueError: If data is invalid and strict=True
    """
    # This would be implemented when the actual validation classes are created
    raise NotImplementedError("validate_data will be implemented with validation classes")

def format_currency(value, currency='USD', precision=2):
    """Format currency values for display.
    
    Args:
        value: Numeric value to format
        currency: Currency symbol
        precision: Decimal precision
        
    Returns:
        str: Formatted currency string
    """
    # This would be implemented when the actual formatter classes are created
    raise NotImplementedError("format_currency will be implemented with formatter classes")

def calculate_returns(prices, method='simple'):
    """Calculate returns from price data.
    
    Args:
        prices: Price series or array
        method: Return calculation method ('simple', 'log')
        
    Returns:
        array: Returns array
    """
    # This would be implemented when the actual math utility classes are created
    raise NotImplementedError("calculate_returns will be implemented with math utility classes")

# Export public API
__all__ = [
    "Config",
    "Logger",
    "DataValidator",
    "FileManager",
    "TimeUtils",
    "MathUtils",
    "CurrencyFormatter",
    "PercentFormatter",
    "setup_logging",
    "load_config",
    "validate_data",
    "format_currency",
    "calculate_returns",
    "timing",
    "retry",
    "cache",
    "CONFIG_PATHS",
    "DEFAULT_LOGGING_CONFIG",
    "VALIDATION_RULES",
    "FILE_SETTINGS",
    "TIME_ZONES",
    "MARKET_HOURS",
    "MATH_CONSTANTS",
    "PERFORMANCE_THRESHOLDS",
]