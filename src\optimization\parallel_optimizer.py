"""Parallel optimization module for cryptocurrency backtesting system.

This module provides efficient parameter optimization using multiple CPU cores,
supporting various optimization algorithms and parallel execution strategies.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Union, Callable, Iterator
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, field
from enum import Enum
import threading
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import queue
import time
import pickle
import json
from pathlib import Path
import itertools
from collections import defaultdict
import psutil
import gc

# Optimization libraries
from sklearn.model_selection import ParameterGrid
from skopt import gp_minimize, forest_minimize, gbrt_minimize
from skopt.space import Real, Integer, Categorical
from skopt.utils import use_named_args
import optuna
from joblib import Parallel, delayed

from ..engine.backtest_engine import BacktestEngine, BacktestConfig, BacktestResult
from ..strategies.base_strategy import BaseStrategy
from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class OptimizationMethod(Enum):
    """Optimization methods."""
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    BAYESIAN_GP = "bayesian_gp"
    BAYESIAN_RF = "bayesian_rf"
    BAYESIAN_GBRT = "bayesian_gbrt"
    OPTUNA_TPE = "optuna_tpe"
    OPTUNA_CMAES = "optuna_cmaes"
    GENETIC_ALGORITHM = "genetic_algorithm"


class ExecutionMode(Enum):
    """Execution modes."""
    SEQUENTIAL = "sequential"
    THREAD_POOL = "thread_pool"
    PROCESS_POOL = "process_pool"
    DISTRIBUTED = "distributed"


@dataclass
class ParameterSpace:
    """Parameter space definition."""
    name: str
    type: str  # 'int', 'float', 'categorical'
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    step: Optional[Union[int, float]] = None
    choices: Optional[List[Any]] = None
    log_scale: bool = False
    
    def to_skopt_dimension(self):
        """Convert to scikit-optimize dimension."""
        if self.type == 'int':
            return Integer(self.min_value, self.max_value, name=self.name)
        elif self.type == 'float':
            if self.log_scale:
                return Real(self.min_value, self.max_value, prior='log-uniform', name=self.name)
            else:
                return Real(self.min_value, self.max_value, name=self.name)
        elif self.type == 'categorical':
            return Categorical(self.choices, name=self.name)
        else:
            raise ValueError(f"Unknown parameter type: {self.type}")
    
    def to_optuna_suggest(self, trial):
        """Convert to Optuna suggestion."""
        if self.type == 'int':
            return trial.suggest_int(self.name, self.min_value, self.max_value, step=self.step)
        elif self.type == 'float':
            if self.log_scale:
                return trial.suggest_float(self.name, self.min_value, self.max_value, log=True, step=self.step)
            else:
                return trial.suggest_float(self.name, self.min_value, self.max_value, step=self.step)
        elif self.type == 'categorical':
            return trial.suggest_categorical(self.name, self.choices)
        else:
            raise ValueError(f"Unknown parameter type: {self.type}")


@dataclass
class OptimizationConfig:
    """Optimization configuration."""
    method: OptimizationMethod
    execution_mode: ExecutionMode
    parameter_space: List[ParameterSpace]
    objective_metric: str = "sharpe_ratio"  # Metric to optimize
    maximize: bool = True  # Whether to maximize the objective
    n_trials: int = 100  # Number of trials for random/bayesian methods
    n_jobs: int = -1  # Number of parallel jobs (-1 = all cores)
    timeout: Optional[int] = None  # Timeout in seconds
    early_stopping_rounds: Optional[int] = None
    cv_folds: int = 1  # Cross-validation folds
    test_size: float = 0.2  # Test set size for validation
    random_state: int = 42
    save_intermediate: bool = True
    intermediate_path: Optional[str] = None
    memory_limit_gb: float = 16.0  # Memory limit per worker
    

@dataclass
class OptimizationResult:
    """Single optimization trial result."""
    trial_id: int
    parameters: Dict[str, Any]
    objective_value: float
    metrics: Dict[str, float]
    backtest_result: Optional[BacktestResult] = None
    execution_time: float = 0.0
    memory_usage_mb: float = 0.0
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class OptimizationSummary:
    """Optimization summary results."""
    config: OptimizationConfig
    best_result: OptimizationResult
    all_results: List[OptimizationResult]
    total_trials: int
    successful_trials: int
    failed_trials: int
    total_time: timedelta
    best_parameters: Dict[str, Any]
    parameter_importance: Dict[str, float]
    convergence_history: List[float]
    

class ParameterGenerator:
    """Generates parameter combinations for optimization."""
    
    def __init__(self, parameter_space: List[ParameterSpace], method: OptimizationMethod, n_trials: int = 100, random_state: int = 42):
        self.parameter_space = parameter_space
        self.method = method
        self.n_trials = n_trials
        self.random_state = random_state
        self.rng = np.random.RandomState(random_state)
        
    def generate_grid(self) -> Iterator[Dict[str, Any]]:
        """Generate grid search parameters."""
        param_dict = {}
        
        for param in self.parameter_space:
            if param.type == 'int':
                step = param.step or 1
                param_dict[param.name] = list(range(param.min_value, param.max_value + 1, step))
            elif param.type == 'float':
                if param.step:
                    values = np.arange(param.min_value, param.max_value + param.step, param.step)
                else:
                    values = np.linspace(param.min_value, param.max_value, 10)
                param_dict[param.name] = values.tolist()
            elif param.type == 'categorical':
                param_dict[param.name] = param.choices
        
        for params in ParameterGrid(param_dict):
            yield params
    
    def generate_random(self) -> Iterator[Dict[str, Any]]:
        """Generate random search parameters."""
        for _ in range(self.n_trials):
            params = {}
            
            for param in self.parameter_space:
                if param.type == 'int':
                    params[param.name] = self.rng.randint(param.min_value, param.max_value + 1)
                elif param.type == 'float':
                    if param.log_scale:
                        log_min = np.log(param.min_value)
                        log_max = np.log(param.max_value)
                        params[param.name] = np.exp(self.rng.uniform(log_min, log_max))
                    else:
                        params[param.name] = self.rng.uniform(param.min_value, param.max_value)
                elif param.type == 'categorical':
                    params[param.name] = self.rng.choice(param.choices)
            
            yield params


class ObjectiveFunction:
    """Wrapper for objective function evaluation."""
    
    def __init__(
        self,
        strategy_class: type,
        backtest_config: BacktestConfig,
        symbols: List[str],
        objective_metric: str = "sharpe_ratio",
        maximize: bool = True
    ):
        self.strategy_class = strategy_class
        self.backtest_config = backtest_config
        self.symbols = symbols
        self.objective_metric = objective_metric
        self.maximize = maximize
        self.logger = get_logger("objective_function")
    
    def __call__(self, parameters: Dict[str, Any]) -> OptimizationResult:
        """Evaluate objective function.
        
        Args:
            parameters: Strategy parameters
            
        Returns:
            Optimization result
        """
        start_time = time.time()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            # Create strategy instance with parameters
            strategy = self.strategy_class(
                strategy_id=f"opt_{int(time.time())}",
                symbols=self.symbols,
                **parameters
            )
            
            # Create backtest engine
            engine = BacktestEngine(self.backtest_config)
            engine.add_strategy(strategy)
            
            # Run backtest
            results = engine.run(self.symbols)
            backtest_result = results[strategy.strategy_id]
            
            # Extract metrics
            metrics = backtest_result.custom_metrics
            objective_value = metrics.get(self.objective_metric, 0.0)
            
            # Adjust for maximization/minimization
            if not self.maximize:
                objective_value = -objective_value
            
            # Calculate execution time and memory usage
            execution_time = time.time() - start_time
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage = final_memory - initial_memory
            
            return OptimizationResult(
                trial_id=0,  # Will be set by optimizer
                parameters=parameters,
                objective_value=objective_value,
                metrics=metrics,
                backtest_result=backtest_result,
                execution_time=execution_time,
                memory_usage_mb=memory_usage
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Objective function failed: {e}")
            
            return OptimizationResult(
                trial_id=0,
                parameters=parameters,
                objective_value=float('-inf') if self.maximize else float('inf'),
                metrics={},
                execution_time=execution_time,
                error=str(e)
            )
        
        finally:
            # Force garbage collection
            gc.collect()


class ParallelOptimizer:
    """Main parallel optimization engine."""
    
    def __init__(self, config: OptimizationConfig):
        """Initialize optimizer.
        
        Args:
            config: Optimization configuration
        """
        self.config = config
        self.logger = get_logger("parallel_optimizer")
        
        # Results storage
        self.results: List[OptimizationResult] = []
        self.best_result: Optional[OptimizationResult] = None
        self.convergence_history: List[float] = []
        
        # Threading
        self._stop_event = threading.Event()
        self._lock = threading.Lock()
        
        # Progress tracking
        self.completed_trials = 0
        self.start_time: Optional[datetime] = None
        
        # Set number of jobs
        if self.config.n_jobs == -1:
            self.config.n_jobs = mp.cpu_count()
        
        self.logger.info(f"Initialized optimizer with {self.config.n_jobs} workers")
    
    def optimize(
        self,
        strategy_class: type,
        backtest_config: BacktestConfig,
        symbols: List[str]
    ) -> OptimizationSummary:
        """Run optimization.
        
        Args:
            strategy_class: Strategy class to optimize
            backtest_config: Backtesting configuration
            symbols: List of symbols to trade
            
        Returns:
            Optimization summary
        """
        self.start_time = datetime.now()
        self.logger.info(f"Starting optimization with method: {self.config.method.value}")
        
        try:
            # Create objective function
            objective_fn = ObjectiveFunction(
                strategy_class=strategy_class,
                backtest_config=backtest_config,
                symbols=symbols,
                objective_metric=self.config.objective_metric,
                maximize=self.config.maximize
            )
            
            # Run optimization based on method
            if self.config.method == OptimizationMethod.GRID_SEARCH:
                self._run_grid_search(objective_fn)
            elif self.config.method == OptimizationMethod.RANDOM_SEARCH:
                self._run_random_search(objective_fn)
            elif self.config.method in [OptimizationMethod.BAYESIAN_GP, OptimizationMethod.BAYESIAN_RF, OptimizationMethod.BAYESIAN_GBRT]:
                self._run_bayesian_optimization(objective_fn)
            elif self.config.method in [OptimizationMethod.OPTUNA_TPE, OptimizationMethod.OPTUNA_CMAES]:
                self._run_optuna_optimization(objective_fn)
            else:
                raise ValueError(f"Unsupported optimization method: {self.config.method}")
            
        except Exception as e:
            self.logger.error(f"Optimization failed: {e}")
            raise
        
        # Generate summary
        return self._generate_summary()
    
    def _run_grid_search(self, objective_fn: ObjectiveFunction) -> None:
        """Run grid search optimization."""
        generator = ParameterGenerator(
            self.config.parameter_space,
            OptimizationMethod.GRID_SEARCH,
            random_state=self.config.random_state
        )
        
        param_combinations = list(generator.generate_grid())
        total_combinations = len(param_combinations)
        
        self.logger.info(f"Grid search: {total_combinations} parameter combinations")
        
        if self.config.execution_mode == ExecutionMode.SEQUENTIAL:
            self._run_sequential(objective_fn, param_combinations)
        else:
            self._run_parallel(objective_fn, param_combinations)
    
    def _run_random_search(self, objective_fn: ObjectiveFunction) -> None:
        """Run random search optimization."""
        generator = ParameterGenerator(
            self.config.parameter_space,
            OptimizationMethod.RANDOM_SEARCH,
            n_trials=self.config.n_trials,
            random_state=self.config.random_state
        )
        
        param_combinations = list(generator.generate_random())
        
        self.logger.info(f"Random search: {len(param_combinations)} parameter combinations")
        
        if self.config.execution_mode == ExecutionMode.SEQUENTIAL:
            self._run_sequential(objective_fn, param_combinations)
        else:
            self._run_parallel(objective_fn, param_combinations)
    
    def _run_bayesian_optimization(self, objective_fn: ObjectiveFunction) -> None:
        """Run Bayesian optimization."""
        # Convert parameter space to scikit-optimize format
        dimensions = [param.to_skopt_dimension() for param in self.config.parameter_space]
        
        # Create objective function wrapper
        @use_named_args(dimensions)
        def skopt_objective(**params):
            result = objective_fn(params)
            self._update_results(result)
            
            # Return negative value for minimization (scikit-optimize minimizes)
            return -result.objective_value if self.config.maximize else result.objective_value
        
        # Choose optimizer
        if self.config.method == OptimizationMethod.BAYESIAN_GP:
            optimizer_func = gp_minimize
        elif self.config.method == OptimizationMethod.BAYESIAN_RF:
            optimizer_func = forest_minimize
        elif self.config.method == OptimizationMethod.BAYESIAN_GBRT:
            optimizer_func = gbrt_minimize
        
        # Run optimization
        result = optimizer_func(
            func=skopt_objective,
            dimensions=dimensions,
            n_calls=self.config.n_trials,
            n_jobs=self.config.n_jobs if self.config.execution_mode != ExecutionMode.SEQUENTIAL else 1,
            random_state=self.config.random_state
        )
        
        self.logger.info(f"Bayesian optimization completed: {len(self.results)} trials")
    
    def _run_optuna_optimization(self, objective_fn: ObjectiveFunction) -> None:
        """Run Optuna optimization."""
        # Create study
        direction = "maximize" if self.config.maximize else "minimize"
        
        if self.config.method == OptimizationMethod.OPTUNA_TPE:
            sampler = optuna.samplers.TPESampler(seed=self.config.random_state)
        elif self.config.method == OptimizationMethod.OPTUNA_CMAES:
            sampler = optuna.samplers.CmaEsSampler(seed=self.config.random_state)
        
        study = optuna.create_study(direction=direction, sampler=sampler)
        
        # Define objective function
        def optuna_objective(trial):
            # Generate parameters
            params = {}
            for param in self.config.parameter_space:
                params[param.name] = param.to_optuna_suggest(trial)
            
            # Evaluate
            result = objective_fn(params)
            self._update_results(result)
            
            return result.objective_value
        
        # Run optimization
        study.optimize(
            optuna_objective,
            n_trials=self.config.n_trials,
            timeout=self.config.timeout,
            n_jobs=self.config.n_jobs if self.config.execution_mode != ExecutionMode.SEQUENTIAL else 1
        )
        
        self.logger.info(f"Optuna optimization completed: {len(study.trials)} trials")
    
    def _run_sequential(self, objective_fn: ObjectiveFunction, param_combinations: List[Dict[str, Any]]) -> None:
        """Run optimization sequentially."""
        for i, params in enumerate(param_combinations):
            if self._stop_event.is_set():
                break
            
            result = objective_fn(params)
            result.trial_id = i
            self._update_results(result)
            
            # Progress logging
            if (i + 1) % 10 == 0:
                progress = ((i + 1) / len(param_combinations)) * 100
                self.logger.info(f"Progress: {progress:.1f}% ({i + 1}/{len(param_combinations)})")
    
    def _run_parallel(self, objective_fn: ObjectiveFunction, param_combinations: List[Dict[str, Any]]) -> None:
        """Run optimization in parallel."""
        if self.config.execution_mode == ExecutionMode.THREAD_POOL:
            executor_class = ThreadPoolExecutor
        elif self.config.execution_mode == ExecutionMode.PROCESS_POOL:
            executor_class = ProcessPoolExecutor
        else:
            raise ValueError(f"Unsupported execution mode: {self.config.execution_mode}")
        
        with executor_class(max_workers=self.config.n_jobs) as executor:
            # Submit all tasks
            future_to_params = {
                executor.submit(objective_fn, params): (i, params)
                for i, params in enumerate(param_combinations)
            }
            
            # Collect results
            for future in as_completed(future_to_params):
                if self._stop_event.is_set():
                    break
                
                trial_id, params = future_to_params[future]
                
                try:
                    result = future.result()
                    result.trial_id = trial_id
                    self._update_results(result)
                    
                except Exception as e:
                    self.logger.error(f"Trial {trial_id} failed: {e}")
                    
                    # Create error result
                    error_result = OptimizationResult(
                        trial_id=trial_id,
                        parameters=params,
                        objective_value=float('-inf') if self.config.maximize else float('inf'),
                        metrics={},
                        error=str(e)
                    )
                    self._update_results(error_result)
                
                # Progress logging
                if len(self.results) % 10 == 0:
                    progress = (len(self.results) / len(param_combinations)) * 100
                    self.logger.info(f"Progress: {progress:.1f}% ({len(self.results)}/{len(param_combinations)})")
    
    def _update_results(self, result: OptimizationResult) -> None:
        """Update results and best result.
        
        Args:
            result: New optimization result
        """
        with self._lock:
            self.results.append(result)
            self.completed_trials += 1
            
            # Update best result
            if result.error is None:
                if self.best_result is None:
                    self.best_result = result
                else:
                    if self.config.maximize:
                        if result.objective_value > self.best_result.objective_value:
                            self.best_result = result
                    else:
                        if result.objective_value < self.best_result.objective_value:
                            self.best_result = result
                
                # Update convergence history
                self.convergence_history.append(self.best_result.objective_value)
            
            # Save intermediate results
            if self.config.save_intermediate and len(self.results) % 50 == 0:
                self._save_intermediate_results()
    
    def _save_intermediate_results(self) -> None:
        """Save intermediate results to disk."""
        if not self.config.intermediate_path:
            return
        
        try:
            path = Path(self.config.intermediate_path)
            path.mkdir(parents=True, exist_ok=True)
            
            # Save results
            results_file = path / f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            with open(results_file, 'wb') as f:
                pickle.dump(self.results, f)
            
            # Save best result
            if self.best_result:
                best_file = path / "best_result.json"
                with open(best_file, 'w') as f:
                    json.dump({
                        'parameters': self.best_result.parameters,
                        'objective_value': self.best_result.objective_value,
                        'metrics': self.best_result.metrics
                    }, f, indent=2)
            
            self.logger.debug(f"Saved intermediate results to {results_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save intermediate results: {e}")
    
    def _generate_summary(self) -> OptimizationSummary:
        """Generate optimization summary.
        
        Returns:
            Optimization summary
        """
        end_time = datetime.now()
        total_time = end_time - self.start_time if self.start_time else timedelta(0)
        
        successful_results = [r for r in self.results if r.error is None]
        failed_results = [r for r in self.results if r.error is not None]
        
        # Calculate parameter importance (simplified)
        parameter_importance = self._calculate_parameter_importance(successful_results)
        
        summary = OptimizationSummary(
            config=self.config,
            best_result=self.best_result,
            all_results=self.results,
            total_trials=len(self.results),
            successful_trials=len(successful_results),
            failed_trials=len(failed_results),
            total_time=total_time,
            best_parameters=self.best_result.parameters if self.best_result else {},
            parameter_importance=parameter_importance,
            convergence_history=self.convergence_history
        )
        
        self.logger.info(
            f"Optimization completed: {len(self.results)} trials, "
            f"best {self.config.objective_metric}: {self.best_result.objective_value if self.best_result else 'N/A'}"
        )
        
        return summary
    
    def _calculate_parameter_importance(self, results: List[OptimizationResult]) -> Dict[str, float]:
        """Calculate parameter importance (simplified correlation analysis).
        
        Args:
            results: List of successful results
            
        Returns:
            Parameter importance scores
        """
        if len(results) < 2:
            return {}
        
        try:
            # Create DataFrame
            data = []
            for result in results:
                row = result.parameters.copy()
                row['objective'] = result.objective_value
                data.append(row)
            
            df = pd.DataFrame(data)
            
            # Calculate correlations
            correlations = df.corr()['objective'].abs().drop('objective')
            
            # Normalize to sum to 1
            total = correlations.sum()
            if total > 0:
                importance = (correlations / total).to_dict()
            else:
                importance = {param: 0.0 for param in correlations.index}
            
            return importance
            
        except Exception as e:
            self.logger.error(f"Failed to calculate parameter importance: {e}")
            return {}
    
    def stop(self) -> None:
        """Stop optimization."""
        self._stop_event.set()
        self.logger.info("Optimization stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get optimization status.
        
        Returns:
            Status dictionary
        """
        elapsed_time = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        return {
            'method': self.config.method.value,
            'execution_mode': self.config.execution_mode.value,
            'completed_trials': self.completed_trials,
            'total_trials': self.config.n_trials,
            'elapsed_time': elapsed_time.total_seconds(),
            'best_objective': self.best_result.objective_value if self.best_result else None,
            'best_parameters': self.best_result.parameters if self.best_result else {},
            'successful_trials': len([r for r in self.results if r.error is None]),
            'failed_trials': len([r for r in self.results if r.error is not None])
        }


# Utility functions
def create_parameter_space(
    int_params: Optional[Dict[str, Tuple[int, int]]] = None,
    float_params: Optional[Dict[str, Tuple[float, float]]] = None,
    categorical_params: Optional[Dict[str, List[Any]]] = None,
    log_params: Optional[List[str]] = None
) -> List[ParameterSpace]:
    """Create parameter space from simple definitions.
    
    Args:
        int_params: Integer parameters {name: (min, max)}
        float_params: Float parameters {name: (min, max)}
        categorical_params: Categorical parameters {name: [choices]}
        log_params: Parameters to use log scale
        
    Returns:
        List of parameter space definitions
    """
    parameter_space = []
    log_params = log_params or []
    
    if int_params:
        for name, (min_val, max_val) in int_params.items():
            parameter_space.append(ParameterSpace(
                name=name,
                type='int',
                min_value=min_val,
                max_value=max_val,
                log_scale=name in log_params
            ))
    
    if float_params:
        for name, (min_val, max_val) in float_params.items():
            parameter_space.append(ParameterSpace(
                name=name,
                type='float',
                min_value=min_val,
                max_value=max_val,
                log_scale=name in log_params
            ))
    
    if categorical_params:
        for name, choices in categorical_params.items():
            parameter_space.append(ParameterSpace(
                name=name,
                type='categorical',
                choices=choices
            ))
    
    return parameter_space


def run_optimization(
    strategy_class: type,
    symbols: List[str],
    start_date: datetime,
    end_date: datetime,
    parameter_space: List[ParameterSpace],
    method: OptimizationMethod = OptimizationMethod.RANDOM_SEARCH,
    n_trials: int = 100,
    n_jobs: int = -1,
    objective_metric: str = "sharpe_ratio",
    **kwargs
) -> OptimizationSummary:
    """Convenience function to run optimization.
    
    Args:
        strategy_class: Strategy class to optimize
        symbols: List of symbols to trade
        start_date: Backtest start date
        end_date: Backtest end date
        parameter_space: Parameter space to optimize
        method: Optimization method
        n_trials: Number of trials
        n_jobs: Number of parallel jobs
        objective_metric: Metric to optimize
        **kwargs: Additional configuration options
        
    Returns:
        Optimization summary
    """
    # Create configurations
    backtest_config = BacktestConfig(
        start_date=start_date,
        end_date=end_date,
        **{k: v for k, v in kwargs.items() if k in BacktestConfig.__dataclass_fields__}
    )
    
    optimization_config = OptimizationConfig(
        method=method,
        execution_mode=ExecutionMode.PROCESS_POOL,
        parameter_space=parameter_space,
        objective_metric=objective_metric,
        n_trials=n_trials,
        n_jobs=n_jobs,
        **{k: v for k, v in kwargs.items() if k in OptimizationConfig.__dataclass_fields__}
    )
    
    # Run optimization
    optimizer = ParallelOptimizer(optimization_config)
    return optimizer.optimize(strategy_class, backtest_config, symbols)