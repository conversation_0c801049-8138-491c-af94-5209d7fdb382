# Cryptocurrency Backtesting System

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://www.docker.com/)

An advanced cryptocurrency backtesting system with parallel optimization capabilities, built for high-performance strategy development and testing.

## 🚀 Features

### Core Backtesting Engine
- **Event-Driven Architecture**: Real-time strategy execution simulation
- **Multi-Exchange Support**: Fetch data from 100+ cryptocurrency exchanges via CCXT
- **Advanced Order Types**: Market, limit, stop-loss, take-profit orders
- **Portfolio Management**: Multi-asset portfolio tracking and risk management
- **Performance Tracking**: Comprehensive metrics and analytics

### Parallel Optimization
- **Multiple Algorithms**: Grid search, random search, Bayesian optimization, Optuna
- **Distributed Computing**: Ray and Dask support for cluster computing
- **Memory Efficient**: Smart caching and memory-mapped storage for large datasets
- **Real-time Monitoring**: Live performance metrics and resource usage tracking

### Data Management
- **Automated Data Fetching**: Historical OHLCV data from multiple timeframes
- **Data Quality Assurance**: Automatic gap detection, outlier removal, and validation
- **Efficient Storage**: HDF5, Parquet, and Redis caching for fast data access
- **Technical Indicators**: Built-in technical analysis indicators

### Performance Analysis
- **Comprehensive Metrics**: Sharpe ratio, Sortino ratio, maximum drawdown, and more
- **Risk Analysis**: VaR, CVaR, beta, correlation analysis
- **Interactive Visualizations**: Plotly-based charts and performance dashboards
- **Benchmark Comparison**: Compare strategies against market indices

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Layer    │    │  Strategy Layer │    │ Execution Layer │
│                 │    │                 │    │                 │
│ • Data Fetcher  │───▶│ • Base Strategy │───▶│ • Backtest Eng. │
│ • Preprocessor  │    │ • Custom Strats │    │ • Order Manager │
│ • Storage       │    │ • Indicators    │    │ • Portfolio Mgr │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Optimization    │    │   Monitoring    │    │    Analysis     │
│                 │    │                 │    │                 │
│ • Parallel Opt. │    │ • Metrics       │    │ • Performance   │
│ • Parameter Gen │    │ • Alerts        │    │ • Risk Analysis │
│ • Result Agg.   │    │ • System Mon.   │    │ • Reporting     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

- **Core Engine**: Custom event-driven backtesting engine
- **Data Processing**: Pandas, NumPy, TA-Lib
- **Parallel Computing**: Ray, Dask, multiprocessing, Optuna
- **Data Storage**: HDF5, Parquet, Redis, PostgreSQL
- **Cryptocurrency Data**: CCXT
- **Visualization**: Plotly, Matplotlib
- **CLI**: Rich, Click
- **Configuration**: YAML
- **Testing**: pytest
- **Documentation**: Sphinx

## 🔧 Hardware Requirements

### Recommended Specifications
- **CPU**: Multi-core processor (16+ cores recommended)
- **RAM**: 32GB+ (64GB+ for large datasets)
- **Storage**: SSD for data storage and caching
- **Network**: Stable internet connection for data fetching

### Optimized For
- **CPU**: AMD Ryzen 9 7950X (32 threads)
- **RAM**: 96GB DDR5 6000MHz
- **Storage**: NVMe SSD

## 📋 Prerequisites

### Software Requirements
- Python 3.9+
- Docker Desktop (optional, for containerization)
- Git

### Python Dependencies
See `requirements.txt` for complete list of dependencies.

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd Backtester4

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
```bash
# Copy example configuration
cp config/development.yaml.example config/development.yaml

# Edit configuration for your system
# Adjust max_workers based on your CPU cores
# Set memory limits based on available RAM
```

### 3. Data Setup
```python
# Fetch sample cryptocurrency data
from src.data.fetcher import CryptoDataFetcher

fetcher = CryptoDataFetcher()
data = fetcher.fetch_data('BTC/USDT', '1h', '2023-01-01', '2023-12-31')
```

### 4. Run Basic Backtest
```python
from src.strategies.ma_crossover import MovingAverageCrossover
from src.optimization.parallel_optimizer import ParallelOptimizer

# Define parameter grid
param_grid = {
    'fast_period': [10, 20, 30],
    'slow_period': [50, 100, 200]
}

# Run optimization
optimizer = ParallelOptimizer(max_workers=16)
results = optimizer.optimize(MovingAverageCrossover, data, param_grid)

# Find best parameters
best_params = results.get_best_parameters()
print(f"Best parameters: {best_params}")
```

## 📊 Strategy Development

### Creating Custom Strategies

```python
from src.strategies.base_strategy import BaseStrategy
import pandas as pd
from typing import Dict, Any

class CustomStrategy(BaseStrategy):
    """Custom trading strategy implementation."""
    
    def __init__(self, params: Dict[str, Any]):
        super().__init__(params)
        self.indicator_period = params.get('period', 20)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate buy/sell signals based on custom logic."""
        signals = pd.DataFrame(index=data.index)
        
        # Custom indicator calculation
        signals['indicator'] = data['close'].rolling(
            window=self.indicator_period
        ).mean()
        
        # Signal generation logic
        signals['position'] = 0
        signals.loc[data['close'] > signals['indicator'], 'position'] = 1
        signals.loc[data['close'] < signals['indicator'], 'position'] = -1
        
        return signals
    
    def _validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate strategy parameters."""
        if 'period' in params and params['period'] < 1:
            raise ValueError("Period must be positive")
        return params
```

### Available Strategies

- **Moving Average Crossover**: Classic MA crossover strategy
- **RSI Strategy**: Relative Strength Index based signals
- **Bollinger Bands**: Mean reversion strategy
- **MACD Strategy**: Moving Average Convergence Divergence
- **Custom Indicators**: Extensible framework for custom indicators

## ⚡ Performance Optimization

### Parallel Processing Configuration

```yaml
# config/optimization.yaml
parallel_processing:
  max_workers: 28  # Leave cores for system processes
  chunk_size: 1000  # Parameter combinations per chunk
  memory_limit_gb: 80  # 80% of available memory
  
data_processing:
  chunk_size_rows: 100000  # Rows per data chunk
  cache_size_mb: 2048  # Data cache size
  use_memory_mapping: true
```

### Memory Management

- **Data Chunking**: Process large datasets in manageable chunks
- **Memory Monitoring**: Real-time memory usage tracking
- **Garbage Collection**: Explicit cleanup of unused objects
- **Shared Memory**: Efficient data sharing between processes

### CPU Optimization

- **Process Pool**: Distribute work across all CPU cores
- **Load Balancing**: Dynamic task distribution
- **JIT Compilation**: Numba acceleration for numerical operations
- **Vectorization**: NumPy optimized operations

## 📈 Results Analysis

### Performance Metrics

- **Total Return**: Overall strategy performance
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of gross profit to gross loss
- **Calmar Ratio**: Annual return / maximum drawdown

### Alpha Identification

```python
from src.analysis.alpha_finder import AlphaFinder

# Analyze optimization results
alpha_finder = AlphaFinder()
alpha_params = alpha_finder.find_alpha_parameters(
    results,
    min_sharpe_ratio=1.5,
    max_drawdown=0.15,
    min_trades=100
)

print(f"Found {len(alpha_params)} alpha parameter sets")
```

## 🔍 Monitoring and Debugging

### Logging Configuration

```python
import logging
from src.utils.logging import setup_logging

# Configure logging
setup_logging(
    level="INFO",
    log_file="logs/backtester.log",
    include_performance_metrics=True
)
```

### Performance Monitoring

- **Real-time Metrics**: CPU, memory, and I/O monitoring
- **Progress Tracking**: Optimization progress and ETA
- **Error Reporting**: Comprehensive error logging and recovery
- **Resource Alerts**: Warnings for high resource usage

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=src --cov-report=html

# Run performance tests
pytest tests/performance/ -v
```

### Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Scalability and resource usage
- **Stress Tests**: High-load scenario testing

## 📚 Documentation

### Project Documentation

- **[PLANNING.md](PLANNING.md)**: Comprehensive project planning and architecture
- **[TASK.md](TASK.md)**: Detailed task breakdown and implementation roadmap
- **[STYLE_GUIDE.md](STYLE_GUIDE.md)**: Code style and development standards
- **[AI_BEHAVIOR_RULES.md](AI_BEHAVIOR_RULES.md)**: AI assistant guidelines and rules

### API Documentation

```bash
# Generate API documentation
sphinx-build -b html docs/ docs/_build/html
```

## 🐳 Docker Support

### Building Container

```bash
# Build Docker image
docker build -t crypto-backtester .

# Run container
docker run -v $(pwd)/data:/app/data crypto-backtester
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  backtester:
    build: .
    volumes:
      - ./data:/app/data
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
    command: python -m src.main
```

## 🤝 Contributing

### Development Workflow

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Follow code style guidelines in `STYLE_GUIDE.md`
4. Add comprehensive tests
5. Update documentation
6. Submit pull request

### Code Quality

- Follow PEP 8 standards
- Use type hints for all functions
- Write comprehensive docstrings
- Maintain >90% test coverage
- Include performance considerations

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Troubleshooting

- Check system requirements and hardware compatibility
- Verify configuration settings match your system
- Review log files for error details
- Monitor resource usage during execution

### Performance Issues

- Reduce `max_workers` if experiencing memory pressure
- Increase `chunk_size` for better memory efficiency
- Use SSD storage for better I/O performance
- Monitor CPU temperature during intensive operations

### Common Issues

1. **Memory Errors**: Reduce chunk size or max workers
2. **Slow Performance**: Check CPU utilization and thermal throttling
3. **Data Errors**: Verify data quality and network connectivity
4. **Process Hangs**: Check for deadlocks in parallel processing

## 🔮 Future Enhancements

- **GPU Acceleration**: CUDA support for indicator calculations
- **Distributed Computing**: Multi-machine optimization
- **Machine Learning**: ML-based parameter optimization
- **Real-time Trading**: Live trading integration
- **Web Interface**: Browser-based monitoring and control
- **Advanced Analytics**: Enhanced performance analysis tools

---

**Built for high-performance cryptocurrency strategy optimization**

*Leveraging the full power of modern multi-core systems while maintaining backtesting integrity and quality.*