"""Configuration management module.

This module provides configuration management functionality for the
crypto backtesting system, supporting YAML files and environment variables.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
import logging


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str = "localhost"
    port: int = 5432
    username: str = "backtester"
    password: str = "password"
    database: str = "backtester"
    url: Optional[str] = None
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False
    pool_timeout: int = 30
    pool_recycle: int = 3600
    
    def __post_init__(self):
        """Set url if not provided."""
        if self.url is None:
            self.url = f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class RedisConfig:
    """Redis configuration settings."""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    url: Optional[str] = None
    max_connections: int = 20
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    retry_on_timeout: bool = True
    health_check_interval: int = 30
    
    def __post_init__(self):
        """Set url if not provided."""
        if self.url is None:
            auth = f":{self.password}@" if self.password else ""
            self.url = f"redis://{auth}{self.host}:{self.port}/{self.db}"


@dataclass
class ExchangeConfig:
    """Exchange configuration settings."""
    name: str = "binance"
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    sandbox: bool = False
    rate_limit: float = 1.0
    rateLimit: int = 1200
    timeout: int = 30000
    enableRateLimit: bool = True
    options: Optional[dict] = None
    
    def __post_init__(self):
        """Set default options if not provided."""
        if self.options is None:
            self.options = {"defaultType": "spot"}


@dataclass
class BacktestConfig:
    """Backtesting configuration settings."""
    initial_cash: float = 100000.0
    commission: float = 0.001
    slippage: float = 0.0005
    max_workers: int = 16
    chunk_size: int = 1000
    memory_limit_gb: float = 80.0
    default_capital: float = 10000.0
    default_commission: float = 0.001
    max_positions: int = 10
    position_sizing: str = "fixed"
    risk_per_trade: float = 0.02
    max_drawdown: float = 0.20


@dataclass
class LoggingConfig:
    """Logging configuration settings."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    file: Optional[str] = None
    max_file_size: str = "100MB"
    max_size: str = "100MB"
    backup_count: int = 5
    enable_console: bool = True
    enable_file: bool = True
    performance: Optional[dict] = None
    
    def __post_init__(self):
        """Set file_path if file is provided."""
        if self.file and not self.file_path:
            self.file_path = self.file
        if self.performance is None:
            self.performance = {
                "enable": True,
                "threshold": 1.0,
                "include_args": False,
                "include_result": False
            }


@dataclass
class Config:
    """Main configuration class."""
    environment: str = "development"
    debug: bool = False
    
    # Component configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    redis: RedisConfig = field(default_factory=RedisConfig)
    exchange: ExchangeConfig = field(default_factory=ExchangeConfig)
    backtest: BacktestConfig = field(default_factory=BacktestConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # Additional configuration sections
    optimization: Optional[dict] = None
    parallel: Optional[dict] = None
    memory: Optional[dict] = None
    monitoring: Optional[dict] = None
    data: Optional[dict] = None
    strategies: Optional[dict] = None
    api: Optional[dict] = None
    security: Optional[dict] = None
    development: Optional[dict] = None
    
    # Paths
    data_dir: str = "./data"
    logs_dir: str = "./logs"
    cache_dir: str = "./cache"
    config_dir: str = "./config"
    
    def __post_init__(self):
        """Post-initialization processing."""
        # Ensure directories exist
        for dir_path in [self.data_dir, self.logs_dir, self.cache_dir]:
            Path(dir_path).mkdir(parents=True, exist_ok=True)


class ConfigManager:
    """Configuration manager for loading and managing settings."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration manager.
        
        Args:
            config_file: Path to configuration file
        """
        self._config: Optional[Config] = None
        self._config_file = config_file
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from file and environment variables."""
        # Start with default configuration
        config_data = {}
        
        # Load from file if specified
        if self._config_file:
            config_data = self._load_from_file(self._config_file)
        else:
            # Try to find default config file
            config_file = self._find_config_file()
            if config_file:
                config_data = self._load_from_file(config_file)
        
        # Override with environment variables
        config_data = self._load_from_env(config_data)
        
        # Create configuration object
        self._config = self._create_config(config_data)
    
    def _find_config_file(self) -> Optional[str]:
        """Find default configuration file.
        
        Returns:
            Path to configuration file or None
        """
        # Check environment variable
        config_file = os.getenv('CONFIG_FILE')
        if config_file and Path(config_file).exists():
            return config_file
        
        # Check environment-specific files
        environment = os.getenv('ENVIRONMENT', 'development')
        
        possible_paths = [
            f"config/{environment}.yaml",
            f"config/{environment}.yml",
            "config/config.yaml",
            "config/config.yml",
            "config.yaml",
            "config.yml"
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        return None
    
    def _load_from_file(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from YAML file.
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            Configuration dictionary
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logging.warning(f"Failed to load config file {config_file}: {e}")
            return {}
    
    def _load_from_env(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Load configuration from environment variables.
        
        Args:
            config_data: Existing configuration data
            
        Returns:
            Updated configuration dictionary
        """
        # Environment mapping
        env_mapping = {
            'ENVIRONMENT': 'environment',
            'DEBUG': ('debug', bool),
            
            # Database
            'DB_HOST': 'database.host',
            'DB_PORT': ('database.port', int),
            'DB_USERNAME': 'database.username',
            'DB_PASSWORD': 'database.password',
            'DB_DATABASE': 'database.database',
            
            # Redis
            'REDIS_HOST': 'redis.host',
            'REDIS_PORT': ('redis.port', int),
            'REDIS_PASSWORD': 'redis.password',
            'REDIS_DB': ('redis.db', int),
            
            # Exchange
            'EXCHANGE_NAME': 'exchange.name',
            'EXCHANGE_API_KEY': 'exchange.api_key',
            'EXCHANGE_API_SECRET': 'exchange.api_secret',
            'EXCHANGE_SANDBOX': ('exchange.sandbox', bool),
            'EXCHANGE_RATE_LIMIT': ('exchange.rate_limit', float),
            
            # Backtest
            'INITIAL_CASH': ('backtest.initial_cash', float),
            'COMMISSION': ('backtest.commission', float),
            'SLIPPAGE': ('backtest.slippage', float),
            'MAX_WORKERS': ('backtest.max_workers', int),
            'CHUNK_SIZE': ('backtest.chunk_size', int),
            'MEMORY_LIMIT_GB': ('backtest.memory_limit_gb', float),
            
            # Logging
            'LOG_LEVEL': 'logging.level',
            'LOG_FILE': 'logging.file_path',
            
            # Paths
            'DATA_DIR': 'data_dir',
            'LOGS_DIR': 'logs_dir',
            'CACHE_DIR': 'cache_dir',
            'CONFIG_DIR': 'config_dir',
        }
        
        for env_var, config_path in env_mapping.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Handle type conversion
                if isinstance(config_path, tuple):
                    config_path, value_type = config_path
                    try:
                        if value_type == bool:
                            env_value = env_value.lower() in ('true', '1', 'yes', 'on')
                        else:
                            env_value = value_type(env_value)
                    except (ValueError, TypeError):
                        continue
                
                # Set nested configuration value
                self._set_nested_value(config_data, config_path, env_value)
        
        return config_data
    
    def _set_nested_value(self, data: Dict[str, Any], path: str, value: Any) -> None:
        """Set nested dictionary value using dot notation.
        
        Args:
            data: Dictionary to update
            path: Dot-separated path (e.g., 'database.host')
            value: Value to set
        """
        keys = path.split('.')
        current = data
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def _create_config(self, config_data: Dict[str, Any]) -> Config:
        """Create Config object from configuration data.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Config object
        """
        # Create component configurations
        database_config = DatabaseConfig(**config_data.get('database', {}))
        redis_config = RedisConfig(**config_data.get('redis', {}))
        # Handle both 'exchange' and 'exchanges' keys
        exchange_data = config_data.get('exchange', config_data.get('exchanges', {}))
        if isinstance(exchange_data, dict) and 'binance' in exchange_data:
            # If exchanges contains multiple exchanges, use the first one or default
            exchange_data = exchange_data.get('binance', {})
        exchange_config = ExchangeConfig(**exchange_data)
        backtest_config = BacktestConfig(**config_data.get('backtest', {}))
        logging_config = LoggingConfig(**config_data.get('logging', {}))
        
        # Create main configuration
        excluded_sections = ['database', 'redis', 'exchange', 'exchanges', 'backtest', 'logging', 
                           'optimization', 'parallel', 'memory', 'monitoring', 'data',
                           'strategies', 'api', 'security', 'development']
        main_config_data = {
            k: v for k, v in config_data.items()
            if k not in excluded_sections
        }
        
        # Add the additional sections directly
        for section in ['optimization', 'parallel', 'memory', 'monitoring', 'data',
                       'strategies', 'api', 'security', 'development']:
            if section in config_data:
                main_config_data[section] = config_data[section]
        
        return Config(
            database=database_config,
            redis=redis_config,
            exchange=exchange_config,
            backtest=backtest_config,
            logging=logging_config,
            **main_config_data
        )
    
    @property
    def config(self) -> Config:
        """Get current configuration.
        
        Returns:
            Current configuration object
        """
        if self._config is None:
            self._load_config()
        return self._config
    
    def reload(self) -> None:
        """Reload configuration from file and environment."""
        self._load_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation)
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        current = self.config
        
        try:
            for k in keys:
                current = getattr(current, k)
            return current
        except AttributeError:
            return default


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config(config_file: Optional[str] = None) -> Config:
    """Get global configuration instance.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Configuration object
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    
    return _config_manager.config


def reload_config() -> None:
    """Reload global configuration."""
    global _config_manager
    
    if _config_manager is not None:
        _config_manager.reload()


def set_config_file(config_file: str) -> None:
    """Set configuration file and reload.
    
    Args:
        config_file: Path to configuration file
    """
    global _config_manager
    _config_manager = ConfigManager(config_file)