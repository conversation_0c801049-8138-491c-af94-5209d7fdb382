# Cryptocurrency Backtesting System Configuration

# Database Configuration
database:
  url: "postgresql://backtester:password@localhost:5432/backtester"
  pool_size: 10
  max_overflow: 20
  echo: false
  pool_timeout: 30
  pool_recycle: 3600

# Redis Configuration
redis:
  url: "redis://localhost:6379/0"
  max_connections: 20
  socket_timeout: 5
  socket_connect_timeout: 5
  retry_on_timeout: true
  health_check_interval: 30

# Exchange Configuration
exchanges:
  binance:
    sandbox: false
    rateLimit: 1200
    timeout: 30000
    enableRateLimit: true
    options:
      defaultType: "spot"
  
  coinbase:
    sandbox: true
    rateLimit: 10
    timeout: 30000
    enableRateLimit: true
  
  kraken:
    sandbox: false
    rateLimit: 3000
    timeout: 30000
    enableRateLimit: true

# Backtesting Configuration
backtest:
  default_capital: 10000
  default_commission: 0.001
  slippage: 0.0005
  max_positions: 10
  position_sizing: "fixed"  # fixed, percent, kelly
  risk_per_trade: 0.02
  max_drawdown: 0.20

# Optimization Configuration
optimization:
  default_method: "bayesian"  # grid, random, bayesian, optuna
  max_iterations: 1000
  timeout: 3600  # seconds
  n_trials: 100
  n_jobs: -1  # use all available cores
  cv_folds: 5
  test_size: 0.2
  
  # Optuna specific settings
  optuna:
    study_name: "crypto_backtest_optimization"
    direction: "maximize"  # maximize or minimize
    sampler: "TPE"  # TPE, Random, CmaEs
    pruner: "MedianPruner"  # MedianPruner, SuccessiveHalvingPruner

# Parallel Processing Configuration
parallel:
  default_mode: "multiprocessing"  # sequential, threading, multiprocessing, ray, dask
  max_workers: null  # null for auto-detection
  chunk_size: 10
  timeout: 300
  
  # Ray configuration
  ray:
    address: "auto"
    num_cpus: null
    num_gpus: 0
    memory: null
    object_store_memory: null
  
  # Dask configuration
  dask:
    scheduler: "threads"  # threads, processes, synchronous
    n_workers: null
    threads_per_worker: 2

# Memory Management Configuration
memory:
  cache_size: 1000  # MB
  max_memory_usage: 0.8  # 80% of available memory
  gc_threshold: 0.9  # trigger GC at 90% memory usage
  cache_policy: "lru"  # lru, lfu, fifo
  enable_memory_mapping: true
  mmap_threshold: 100  # MB

# Monitoring Configuration
monitoring:
  enable_metrics: true
  metrics_port: 8000
  update_interval: 10  # seconds
  retention_days: 30
  
  # Alert thresholds
  alerts:
    cpu_threshold: 90  # percent
    memory_threshold: 90  # percent
    disk_threshold: 85  # percent
    error_rate_threshold: 0.05  # 5%
    response_time_threshold: 5000  # milliseconds

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "structured"  # simple, detailed, structured
  file: "logs/backtester.log"
  max_size: "100MB"
  backup_count: 5
  enable_console: true
  enable_file: true
  
  # Performance logging
  performance:
    enable: true
    threshold: 1.0  # log operations taking longer than 1 second
    include_args: false
    include_result: false

# Data Configuration
data:
  storage_path: "data/"
  cache_path: "cache/"
  default_timeframe: "1h"
  max_history_days: 365
  
  # Data quality settings
  quality:
    min_data_points: 100
    max_gap_hours: 24
    outlier_threshold: 3.0  # standard deviations
    min_volume_threshold: 1000  # USD
  
  # Preprocessing settings
  preprocessing:
    fill_method: "forward"  # forward, backward, interpolate
    remove_outliers: true
    add_features: true
    normalize: false

# Strategy Configuration
strategies:
  default_lookback: 100
  max_lookback: 1000
  enable_short_selling: false
  enable_margin: false
  
  # Risk management
  risk:
    max_position_size: 0.1  # 10% of portfolio
    stop_loss: 0.05  # 5%
    take_profit: 0.15  # 15%
    trailing_stop: 0.03  # 3%

# API Configuration
api:
  host: "0.0.0.0"
  port: 8080
  debug: false
  reload: false
  workers: 1
  
  # CORS settings
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]

# Security Configuration
security:
  secret_key: "your-secret-key-here"  # Change this in production
  algorithm: "HS256"
  access_token_expire_minutes: 30
  
  # Rate limiting
  rate_limit:
    requests_per_minute: 60
    burst_size: 10

# Development Configuration
development:
  debug: true
  hot_reload: true
  profiling: false
  testing: false
  
  # Mock data settings
  mock_data:
    enable: false
    seed: 42
    noise_level: 0.01