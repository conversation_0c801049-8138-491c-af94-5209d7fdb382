# API Reference - Crypto Backtesting System

## Overview

This document provides comprehensive API reference for the Crypto Backtesting System, designed for high-performance parameter optimization on Ryzen 9 7950X with 96GB DDR5 RAM.

## Table of Contents

1. [Core Engine API](#core-engine-api)
2. [Strategy Framework API](#strategy-framework-api)
3. [Data Management API](#data-management-api)
4. [Optimization API](#optimization-api)
5. [Parallel Processing API](#parallel-processing-api)
6. [Analysis API](#analysis-api)
7. [Memory Management API](#memory-management-api)
8. [Monitoring API](#monitoring-api)
9. [CLI API](#cli-api)
10. [Configuration API](#configuration-api)

---

## Core Engine API

### BacktestEngine

Main backtesting engine built on Backtrader framework.

```python
from src.engine import BacktestEngine

class BacktestEngine:
    """High-performance backtesting engine with advanced features."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        data_manager: DataManager,
        memory_manager: Optional[MemoryManager] = None
    ) -> None:
        """Initialize backtesting engine.
        
        Args:
            config: Engine configuration dictionary
            data_manager: Data management instance
            memory_manager: Optional memory management instance
        """
    
    def add_strategy(
        self,
        strategy: BaseStrategy,
        parameters: Optional[Dict[str, Any]] = None
    ) -> str:
        """Add strategy to engine.
        
        Args:
            strategy: Strategy instance
            parameters: Optional strategy parameters
            
        Returns:
            Strategy ID for reference
        """
    
    def add_data(
        self,
        symbol: str,
        data: pd.DataFrame,
        timeframe: str = '1d'
    ) -> None:
        """Add market data to engine.
        
        Args:
            symbol: Trading symbol (e.g., 'BTC/USD')
            data: OHLCV data with datetime index
            timeframe: Data timeframe ('1m', '5m', '1h', '1d')
        """
    
    def run(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        initial_cash: float = 100000.0
    ) -> BacktestResult:
        """Execute backtest.
        
        Args:
            start_date: Backtest start date (YYYY-MM-DD)
            end_date: Backtest end date (YYYY-MM-DD)
            initial_cash: Initial portfolio cash
            
        Returns:
            Comprehensive backtest results
        """
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics from last run.
        
        Returns:
            Dictionary of performance metrics
        """
```

### BacktestResult

Comprehensive backtest results container.

```python
from dataclasses import dataclass
from typing import Dict, List, Optional
import pandas as pd

@dataclass
class BacktestResult:
    """Container for backtest results."""
    
    # Basic metrics
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    
    # Time series data
    portfolio_value: pd.Series
    returns: pd.Series
    drawdown: pd.Series
    positions: pd.DataFrame
    trades: pd.DataFrame
    
    # Strategy parameters
    strategy_name: str
    parameters: Dict[str, Any]
    
    # Execution metadata
    start_date: str
    end_date: str
    execution_time: float
    memory_usage: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
    
    def save(self, filepath: str) -> None:
        """Save results to file."""
    
    @classmethod
    def load(cls, filepath: str) -> 'BacktestResult':
        """Load results from file."""
```

---

## Strategy Framework API

### BaseStrategy

Abstract base class for all trading strategies.

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import pandas as pd

class BaseStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, **kwargs):
        """Initialize strategy with parameters."""
        self.parameters = kwargs
        self._indicators = {}
    
    @abstractmethod
    def generate_signals(
        self,
        data: pd.DataFrame
    ) -> pd.Series:
        """Generate trading signals.
        
        Args:
            data: OHLCV market data
            
        Returns:
            Series with trading signals (1: buy, -1: sell, 0: hold)
        """
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """Get strategy parameters for optimization."""
    
    @abstractmethod
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """Set strategy parameters."""
    
    def calculate_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate technical indicators.
        
        Args:
            data: OHLCV market data
            
        Returns:
            Dictionary of calculated indicators
        """
    
    def get_position_size(
        self,
        signal: int,
        current_price: float,
        portfolio_value: float
    ) -> float:
        """Calculate position size for signal.
        
        Args:
            signal: Trading signal (1: buy, -1: sell)
            current_price: Current asset price
            portfolio_value: Current portfolio value
            
        Returns:
            Position size (positive for long, negative for short)
        """
```

### MovingAverageCrossover

Example strategy implementation.

```python
class MovingAverageCrossover(BaseStrategy):
    """Moving average crossover strategy."""
    
    def __init__(
        self,
        fast_period: int = 10,
        slow_period: int = 20,
        position_size: float = 0.95
    ):
        """Initialize MA crossover strategy.
        
        Args:
            fast_period: Fast moving average period
            slow_period: Slow moving average period
            position_size: Position size as fraction of portfolio
        """
        super().__init__(
            fast_period=fast_period,
            slow_period=slow_period,
            position_size=position_size
        )
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """Generate MA crossover signals."""
        fast_ma = data['close'].rolling(self.parameters['fast_period']).mean()
        slow_ma = data['close'].rolling(self.parameters['slow_period']).mean()
        
        signals = pd.Series(0, index=data.index)
        signals[fast_ma > slow_ma] = 1  # Buy signal
        signals[fast_ma < slow_ma] = -1  # Sell signal
        
        return signals
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get parameters for optimization."""
        return {
            'fast_period': {'type': 'int', 'min': 5, 'max': 50},
            'slow_period': {'type': 'int', 'min': 20, 'max': 200},
            'position_size': {'type': 'float', 'min': 0.1, 'max': 1.0}
        }
```

### StrategyFactory

Factory for creating strategy instances.

```python
class StrategyFactory:
    """Factory for creating strategy instances."""
    
    _strategies: Dict[str, Type[BaseStrategy]] = {}
    
    @classmethod
    def register(
        cls,
        name: str,
        strategy_class: Type[BaseStrategy]
    ) -> None:
        """Register strategy class.
        
        Args:
            name: Strategy name
            strategy_class: Strategy class
        """
        cls._strategies[name] = strategy_class
    
    @classmethod
    def create(
        cls,
        name: str,
        **kwargs
    ) -> BaseStrategy:
        """Create strategy instance.
        
        Args:
            name: Strategy name
            **kwargs: Strategy parameters
            
        Returns:
            Strategy instance
        """
        if name not in cls._strategies:
            raise ValueError(f"Unknown strategy: {name}")
        return cls._strategies[name](**kwargs)
    
    @classmethod
    def list_strategies(cls) -> List[str]:
        """List available strategies."""
        return list(cls._strategies.keys())
```

---

## Data Management API

### DataManager

Centralized data management system.

```python
class DataManager:
    """High-performance data management system."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        cache_manager: Optional[CacheManager] = None
    ):
        """Initialize data manager.
        
        Args:
            config: Data configuration
            cache_manager: Optional cache manager
        """
    
    def load_data(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        timeframe: str = '1d',
        source: str = 'binance'
    ) -> pd.DataFrame:
        """Load market data.
        
        Args:
            symbol: Trading symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            timeframe: Data timeframe
            source: Data source
            
        Returns:
            OHLCV DataFrame with datetime index
        """
    
    def save_data(
        self,
        data: pd.DataFrame,
        symbol: str,
        timeframe: str,
        source: str = 'binance'
    ) -> None:
        """Save market data.
        
        Args:
            data: OHLCV DataFrame
            symbol: Trading symbol
            timeframe: Data timeframe
            source: Data source
        """
    
    def get_available_symbols(self, source: str = 'binance') -> List[str]:
        """Get available trading symbols.
        
        Args:
            source: Data source
            
        Returns:
            List of available symbols
        """
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """Validate OHLCV data quality.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            True if data is valid
        """
    
    def resample_data(
        self,
        data: pd.DataFrame,
        target_timeframe: str
    ) -> pd.DataFrame:
        """Resample data to different timeframe.
        
        Args:
            data: Source OHLCV data
            target_timeframe: Target timeframe
            
        Returns:
            Resampled OHLCV data
        """
```

### DataLoader

Efficient data loading with memory optimization.

```python
class DataLoader:
    """Memory-efficient data loader."""
    
    def __init__(
        self,
        chunk_size: int = 100000,
        memory_limit_gb: int = 80
    ):
        """Initialize data loader.
        
        Args:
            chunk_size: Chunk size for large datasets
            memory_limit_gb: Memory limit in GB
        """
    
    def load_large_dataset(
        self,
        file_path: str,
        columns: Optional[List[str]] = None
    ) -> Iterator[pd.DataFrame]:
        """Load large dataset in chunks.
        
        Args:
            file_path: Path to data file
            columns: Columns to load
            
        Yields:
            Data chunks as DataFrames
        """
    
    def stream_process(
        self,
        data_source: Iterator[pd.DataFrame],
        processor: Callable[[pd.DataFrame], pd.DataFrame]
    ) -> Iterator[pd.DataFrame]:
        """Stream process data chunks.
        
        Args:
            data_source: Data chunk iterator
            processor: Processing function
            
        Yields:
            Processed data chunks
        """
```

---

## Optimization API

### ParameterOptimizer

Advanced parameter optimization system.

```python
class ParameterOptimizer:
    """Advanced parameter optimization using multiple algorithms."""
    
    def __init__(
        self,
        engine: BacktestEngine,
        algorithm: str = 'bayesian',
        n_workers: int = 32,  # Optimized for Ryzen 9 7950X
        memory_limit_gb: int = 80
    ):
        """Initialize parameter optimizer.
        
        Args:
            engine: Backtesting engine
            algorithm: Optimization algorithm
            n_workers: Number of parallel workers
            memory_limit_gb: Memory limit in GB
        """
    
    def optimize(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        parameter_space: Dict[str, Dict[str, Any]],
        objective: str = 'sharpe_ratio',
        n_trials: int = 1000,
        timeout: Optional[int] = None
    ) -> OptimizationResult:
        """Optimize strategy parameters.
        
        Args:
            strategy: Strategy to optimize
            data: Market data for backtesting
            parameter_space: Parameter search space
            objective: Optimization objective
            n_trials: Number of optimization trials
            timeout: Timeout in seconds
            
        Returns:
            Optimization results
        """
    
    def multi_objective_optimize(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        parameter_space: Dict[str, Dict[str, Any]],
        objectives: List[str],
        n_trials: int = 1000
    ) -> MultiObjectiveResult:
        """Multi-objective parameter optimization.
        
        Args:
            strategy: Strategy to optimize
            data: Market data
            parameter_space: Parameter search space
            objectives: List of objectives to optimize
            n_trials: Number of trials
            
        Returns:
            Pareto-optimal solutions
        """
```

### OptimizationResult

Optimization results container.

```python
@dataclass
class OptimizationResult:
    """Container for optimization results."""
    
    # Best parameters and performance
    best_parameters: Dict[str, Any]
    best_value: float
    best_backtest: BacktestResult
    
    # Optimization history
    trials: List[Dict[str, Any]]
    convergence_history: List[float]
    
    # Statistics
    n_trials: int
    execution_time: float
    algorithm: str
    objective: str
    
    # Parameter importance
    parameter_importance: Dict[str, float]
    
    def plot_convergence(self) -> None:
        """Plot optimization convergence."""
    
    def plot_parameter_importance(self) -> None:
        """Plot parameter importance."""
    
    def get_top_n_results(self, n: int = 10) -> List[Dict[str, Any]]:
        """Get top N optimization results."""
```

---

## Parallel Processing API

### ParallelExecutor

High-performance parallel execution system.

```python
class ParallelExecutor:
    """Parallel execution optimized for Ryzen 9 7950X."""
    
    def __init__(
        self,
        n_workers: int = 32,
        memory_per_worker_gb: float = 2.5,
        use_gpu: bool = True
    ):
        """Initialize parallel executor.
        
        Args:
            n_workers: Number of worker processes
            memory_per_worker_gb: Memory limit per worker
            use_gpu: Whether to use GPU acceleration
        """
    
    def parallel_backtest(
        self,
        strategies: List[BaseStrategy],
        data: pd.DataFrame,
        parameter_sets: List[Dict[str, Any]]
    ) -> List[BacktestResult]:
        """Execute parallel backtests.
        
        Args:
            strategies: List of strategies
            data: Market data
            parameter_sets: Parameter combinations
            
        Returns:
            List of backtest results
        """
    
    def parallel_optimize(
        self,
        strategy: BaseStrategy,
        data_sets: List[pd.DataFrame],
        parameter_space: Dict[str, Dict[str, Any]]
    ) -> List[OptimizationResult]:
        """Execute parallel optimization across datasets.
        
        Args:
            strategy: Strategy to optimize
            data_sets: List of market datasets
            parameter_space: Parameter search space
            
        Returns:
            List of optimization results
        """
```

### WorkerPool

Managed worker pool for parallel processing.

```python
class WorkerPool:
    """Managed worker pool with resource monitoring."""
    
    def __init__(
        self,
        n_workers: int = 32,
        worker_memory_limit_gb: float = 2.5
    ):
        """Initialize worker pool.
        
        Args:
            n_workers: Number of workers
            worker_memory_limit_gb: Memory limit per worker
        """
    
    def submit_task(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Future:
        """Submit task to worker pool.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Future object for result retrieval
        """
    
    def map(
        self,
        func: Callable,
        iterable: Iterable
    ) -> List[Any]:
        """Map function over iterable in parallel.
        
        Args:
            func: Function to apply
            iterable: Input data
            
        Returns:
            List of results
        """
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """Get worker pool statistics."""
```

---

## Analysis API

### PerformanceAnalyzer

Comprehensive performance analysis system.

```python
class PerformanceAnalyzer:
    """Advanced performance analysis and reporting."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize performance analyzer."""
    
    def analyze_backtest(
        self,
        result: BacktestResult,
        benchmark: Optional[pd.Series] = None
    ) -> AnalysisReport:
        """Analyze backtest performance.
        
        Args:
            result: Backtest result
            benchmark: Optional benchmark returns
            
        Returns:
            Comprehensive analysis report
        """
    
    def compare_strategies(
        self,
        results: List[BacktestResult]
    ) -> ComparisonReport:
        """Compare multiple strategy results.
        
        Args:
            results: List of backtest results
            
        Returns:
            Strategy comparison report
        """
    
    def risk_analysis(
        self,
        returns: pd.Series,
        confidence_level: float = 0.05
    ) -> RiskMetrics:
        """Perform comprehensive risk analysis.
        
        Args:
            returns: Return series
            confidence_level: VaR confidence level
            
        Returns:
            Risk metrics
        """
```

### MetricsCalculator

High-performance metrics calculation.

```python
class MetricsCalculator:
    """Optimized metrics calculation using NumPy/Numba."""
    
    @staticmethod
    def sharpe_ratio(
        returns: pd.Series,
        risk_free_rate: float = 0.02,
        periods_per_year: int = 252
    ) -> float:
        """Calculate annualized Sharpe ratio."""
    
    @staticmethod
    def calmar_ratio(
        returns: pd.Series,
        periods_per_year: int = 252
    ) -> float:
        """Calculate Calmar ratio."""
    
    @staticmethod
    def max_drawdown(portfolio_value: pd.Series) -> float:
        """Calculate maximum drawdown."""
    
    @staticmethod
    def value_at_risk(
        returns: pd.Series,
        confidence_level: float = 0.05
    ) -> float:
        """Calculate Value at Risk."""
    
    @staticmethod
    def conditional_value_at_risk(
        returns: pd.Series,
        confidence_level: float = 0.05
    ) -> float:
        """Calculate Conditional Value at Risk."""
```

---

## Memory Management API

### MemoryManager

Advanced memory management for 96GB RAM optimization.

```python
class MemoryManager:
    """Memory management optimized for 96GB DDR5 RAM."""
    
    def __init__(
        self,
        total_memory_gb: int = 96,
        reserved_memory_gb: int = 16,
        chunk_size_mb: int = 100
    ):
        """Initialize memory manager.
        
        Args:
            total_memory_gb: Total system memory
            reserved_memory_gb: Memory reserved for system
            chunk_size_mb: Default chunk size
        """
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
    
    def optimize_dataframe(
        self,
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """Optimize DataFrame memory usage."""
    
    def chunk_data(
        self,
        data: pd.DataFrame,
        chunk_size: Optional[int] = None
    ) -> Iterator[pd.DataFrame]:
        """Chunk large DataFrame for processing."""
    
    def clear_cache(self) -> None:
        """Clear memory caches."""
    
    def monitor_memory(
        self,
        threshold_gb: float = 75.0
    ) -> None:
        """Monitor memory usage and trigger cleanup."""
```

---

## Monitoring API

### SystemMonitor

Comprehensive system monitoring.

```python
class SystemMonitor:
    """System monitoring optimized for Ryzen 9 7950X setup."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize system monitor."""
    
    def get_cpu_usage(self) -> Dict[str, float]:
        """Get CPU usage statistics."""
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get memory usage statistics."""
    
    def get_gpu_usage(self) -> Dict[str, float]:
        """Get GPU usage statistics."""
    
    def get_disk_usage(self) -> Dict[str, float]:
        """Get disk usage statistics."""
    
    def start_monitoring(
        self,
        interval: int = 5,
        callback: Optional[Callable] = None
    ) -> None:
        """Start continuous monitoring."""
    
    def stop_monitoring(self) -> None:
        """Stop monitoring."""
    
    def generate_report(self) -> MonitoringReport:
        """Generate monitoring report."""
```

---

## CLI API

### CLI Commands

Command-line interface for the backtesting system.

```bash
# Basic backtesting
backtester run --strategy MovingAverageCrossover --symbol BTC/USD --start 2023-01-01 --end 2023-12-31

# Parameter optimization
backtester optimize --strategy MovingAverageCrossover --symbol BTC/USD --trials 1000 --workers 32

# Multi-symbol backtesting
backtester run --strategy RSIMeanReversion --symbols BTC/USD,ETH/USD,ADA/USD --parallel

# Performance analysis
backtester analyze --results results.json --benchmark BTC --output report.html

# Data management
backtester data download --symbol BTC/USD --start 2020-01-01 --end 2023-12-31 --source binance
backtester data validate --symbol BTC/USD --timeframe 1d

# System monitoring
backtester monitor --duration 3600 --output monitoring.json

# Configuration
backtester config show
backtester config set optimization.algorithm bayesian
```

### CLI Configuration

```python
class CLIConfig:
    """CLI configuration management."""
    
    @staticmethod
    def get_default_config() -> Dict[str, Any]:
        """Get default CLI configuration."""
    
    @staticmethod
    def load_config(config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
    
    @staticmethod
    def save_config(
        config: Dict[str, Any],
        config_path: str
    ) -> None:
        """Save configuration to file."""
```

---

## Configuration API

### ConfigManager

Centralized configuration management.

```python
class ConfigManager:
    """Configuration management system."""
    
    def __init__(self, config_path: str):
        """Initialize configuration manager."""
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value."""
    
    def load_environment_config(self) -> None:
        """Load configuration optimized for current environment."""
    
    def validate_config(self) -> bool:
        """Validate configuration completeness and correctness."""
    
    def get_hardware_config(self) -> Dict[str, Any]:
        """Get hardware-optimized configuration."""
```

---

## Error Handling

### Custom Exceptions

```python
class BacktesterException(Exception):
    """Base exception for backtester errors."""
    pass

class DataError(BacktesterException):
    """Data-related errors."""
    pass

class OptimizationError(BacktesterException):
    """Optimization-related errors."""
    pass

class MemoryError(BacktesterException):
    """Memory-related errors."""
    pass

class ConfigurationError(BacktesterException):
    """Configuration-related errors."""
    pass
```

---

## Usage Examples

### Basic Backtesting

```python
from src.engine import BacktestEngine
from src.strategies import MovingAverageCrossover
from src.data import DataManager

# Initialize components
config = ConfigManager('config/development.yaml').get_config()
data_manager = DataManager(config['data'])
engine = BacktestEngine(config['engine'], data_manager)

# Load data
data = data_manager.load_data('BTC/USD', '2023-01-01', '2023-12-31')

# Create and add strategy
strategy = MovingAverageCrossover(fast_period=10, slow_period=20)
engine.add_strategy(strategy)
engine.add_data('BTC/USD', data)

# Run backtest
result = engine.run(initial_cash=100000)
print(f"Sharpe Ratio: {result.sharpe_ratio:.3f}")
print(f"Total Return: {result.total_return:.2%}")
```

### Parameter Optimization

```python
from src.optimization import ParameterOptimizer

# Initialize optimizer
optimizer = ParameterOptimizer(
    engine=engine,
    algorithm='bayesian',
    n_workers=32  # Utilize all Ryzen 9 7950X cores
)

# Define parameter space
parameter_space = {
    'fast_period': {'type': 'int', 'min': 5, 'max': 50},
    'slow_period': {'type': 'int', 'min': 20, 'max': 200}
}

# Optimize
result = optimizer.optimize(
    strategy=MovingAverageCrossover(),
    data=data,
    parameter_space=parameter_space,
    objective='sharpe_ratio',
    n_trials=1000
)

print(f"Best parameters: {result.best_parameters}")
print(f"Best Sharpe ratio: {result.best_value:.3f}")
```

### Parallel Processing

```python
from src.parallel import ParallelExecutor

# Initialize parallel executor
executor = ParallelExecutor(
    n_workers=32,
    memory_per_worker_gb=2.5,
    use_gpu=True
)

# Prepare multiple parameter sets
parameter_sets = [
    {'fast_period': 5, 'slow_period': 20},
    {'fast_period': 10, 'slow_period': 30},
    {'fast_period': 15, 'slow_period': 40},
    # ... more parameter combinations
]

# Execute parallel backtests
strategies = [MovingAverageCrossover() for _ in parameter_sets]
results = executor.parallel_backtest(strategies, data, parameter_sets)

# Analyze results
best_result = max(results, key=lambda r: r.sharpe_ratio)
print(f"Best Sharpe ratio: {best_result.sharpe_ratio:.3f}")
```

This API reference provides comprehensive documentation for all major components of the crypto backtesting system, optimized for your Ryzen 9 7950X setup with 96GB DDR5 RAM.