"""Core backtesting engine for cryptocurrency trading strategies.

This module provides the main backtesting engine that orchestrates
data feeding, strategy execution, order management, and performance tracking.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
import queue
import time
from collections import defaultdict, deque
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..strategies.base_strategy import (
    BaseStrategy, Order, Trade, Position, MarketData,
    OrderStatus, OrderType, OrderSide, PositionSide
)
from ..data.storage import get_storage, get_cache
from ..utils.logging import get_logger, log_performance
from ..utils.config import get_config


class EngineState(Enum):
    """Engine states."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class BacktestConfig:
    """Backtesting configuration."""
    start_date: datetime
    end_date: datetime
    initial_cash: float = 100000.0
    commission_rate: float = 0.001
    slippage: float = 0.0001
    max_positions: int = 10
    position_sizing: str = "fixed"  # fixed, percent, kelly
    risk_free_rate: float = 0.02
    benchmark_symbol: Optional[str] = None
    data_frequency: str = "1h"  # 1m, 5m, 15m, 1h, 4h, 1d
    warmup_period: int = 100  # Number of bars for strategy warmup
    enable_shorting: bool = True
    margin_requirement: float = 1.0
    max_leverage: float = 1.0
    stop_on_error: bool = True
    save_state_interval: int = 1000  # Save state every N bars
    

@dataclass
class BacktestResult:
    """Backtesting results."""
    strategy_id: str
    config: BacktestConfig
    start_time: datetime
    end_time: datetime
    duration: timedelta
    total_bars: int
    processed_bars: int
    
    # Performance metrics
    initial_portfolio_value: float
    final_portfolio_value: float
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    
    # Trading metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    
    # Portfolio metrics
    final_cash: float
    final_positions: Dict[str, Position]
    max_positions_held: int
    
    # Execution metrics
    bars_per_second: float
    memory_usage_mb: float
    
    # Additional data
    equity_curve: List[Tuple[datetime, float]]
    trades: List[Trade]
    orders: List[Order]
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


class OrderManager:
    """Manages order execution and fills."""
    
    def __init__(self, commission_rate: float = 0.001, slippage: float = 0.0001):
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.pending_orders: Dict[str, Order] = {}
        self.filled_orders: List[Order] = []
        self.logger = get_logger("order_manager")
    
    def submit_order(self, order: Order) -> bool:
        """Submit order for execution.
        
        Args:
            order: Order to submit
            
        Returns:
            True if order was accepted
        """
        # Basic validation
        if order.quantity <= 0:
            self.logger.error(f"Invalid order quantity: {order.quantity}")
            return False
        
        if order.type == OrderType.LIMIT and order.price is None:
            self.logger.error("Limit order requires price")
            return False
        
        if order.type in [OrderType.STOP, OrderType.STOP_LIMIT] and order.stop_price is None:
            self.logger.error("Stop order requires stop price")
            return False
        
        self.pending_orders[order.id] = order
        self.logger.debug(f"Order {order.id} submitted")
        return True
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel pending order.
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if order was cancelled
        """
        if order_id in self.pending_orders:
            order = self.pending_orders.pop(order_id)
            order.status = OrderStatus.CANCELLED
            self.logger.debug(f"Order {order_id} cancelled")
            return True
        return False
    
    def process_orders(self, market_data: MarketData) -> List[Tuple[Order, Trade]]:
        """Process pending orders against market data.
        
        Args:
            market_data: Current market data
            
        Returns:
            List of (order, trade) tuples for filled orders
        """
        fills = []
        orders_to_remove = []
        
        for order_id, order in self.pending_orders.items():
            if order.symbol != market_data.symbol:
                continue
            
            fill_price = None
            
            # Check if order should be filled
            if order.type == OrderType.MARKET:
                fill_price = market_data.close
            
            elif order.type == OrderType.LIMIT:
                if order.side == OrderSide.BUY and market_data.low <= order.price:
                    fill_price = min(order.price, market_data.open)
                elif order.side == OrderSide.SELL and market_data.high >= order.price:
                    fill_price = max(order.price, market_data.open)
            
            elif order.type == OrderType.STOP:
                if order.side == OrderSide.BUY and market_data.high >= order.stop_price:
                    fill_price = max(order.stop_price, market_data.open)
                elif order.side == OrderSide.SELL and market_data.low <= order.stop_price:
                    fill_price = min(order.stop_price, market_data.open)
            
            elif order.type == OrderType.STOP_LIMIT:
                # Stop triggered, convert to limit order
                if order.side == OrderSide.BUY and market_data.high >= order.stop_price:
                    if market_data.low <= order.price:
                        fill_price = min(order.price, market_data.open)
                elif order.side == OrderSide.SELL and market_data.low <= order.stop_price:
                    if market_data.high >= order.price:
                        fill_price = max(order.price, market_data.open)
            
            # Execute fill if price determined
            if fill_price is not None:
                trade = self._create_trade(order, fill_price, market_data.timestamp)
                fills.append((order, trade))
                orders_to_remove.append(order_id)
                
                order.status = OrderStatus.FILLED
                order.filled_quantity = order.quantity
                order.filled_price = fill_price
                self.filled_orders.append(order)
        
        # Remove filled orders
        for order_id in orders_to_remove:
            del self.pending_orders[order_id]
        
        return fills
    
    def _create_trade(self, order: Order, fill_price: float, timestamp: datetime) -> Trade:
        """Create trade from filled order.
        
        Args:
            order: Filled order
            fill_price: Fill price
            timestamp: Fill timestamp
            
        Returns:
            Trade object
        """
        # Apply slippage
        if order.side == OrderSide.BUY:
            fill_price *= (1 + self.slippage)
        else:
            fill_price *= (1 - self.slippage)
        
        # Calculate commission
        commission = order.quantity * fill_price * self.commission_rate
        
        trade_id = f"trade_{order.id}_{int(timestamp.timestamp())}"
        
        return Trade(
            id=trade_id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=fill_price,
            timestamp=timestamp,
            commission=commission,
            order_id=order.id,
            strategy_id=order.strategy_id
        )
    
    def get_pending_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get pending orders.
        
        Args:
            symbol: Filter by symbol
            
        Returns:
            List of pending orders
        """
        orders = list(self.pending_orders.values())
        if symbol:
            orders = [o for o in orders if o.symbol == symbol]
        return orders


class PortfolioManager:
    """Manages portfolio state and risk."""
    
    def __init__(self, initial_cash: float, max_positions: int = 10):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.max_positions = max_positions
        self.positions: Dict[str, Position] = {}
        self.equity_curve: List[Tuple[datetime, float]] = []
        self.logger = get_logger("portfolio")
    
    def update_position(self, trade: Trade, current_price: float) -> None:
        """Update position based on trade.
        
        Args:
            trade: Executed trade
            current_price: Current market price
        """
        symbol = trade.symbol
        
        # Update cash
        if trade.side == OrderSide.BUY:
            self.cash -= (trade.quantity * trade.price + trade.commission)
        else:
            self.cash += (trade.quantity * trade.price - trade.commission)
        
        # Update position
        if symbol not in self.positions:
            # Create new position
            side = PositionSide.LONG if trade.side == OrderSide.BUY else PositionSide.SHORT
            quantity = trade.quantity if trade.side == OrderSide.BUY else -trade.quantity
            
            self.positions[symbol] = Position(
                symbol=symbol,
                side=side,
                quantity=quantity,
                entry_price=trade.price,
                current_price=current_price,
                commission=trade.commission,
                entry_time=trade.timestamp
            )
        else:
            # Update existing position
            position = self.positions[symbol]
            
            if trade.side == OrderSide.BUY:
                new_quantity = position.quantity + trade.quantity
            else:
                new_quantity = position.quantity - trade.quantity
            
            if abs(new_quantity) < 1e-8:  # Position closed
                # Calculate realized P&L
                if position.is_long:
                    realized_pnl = (trade.price - position.entry_price) * trade.quantity
                else:
                    realized_pnl = (position.entry_price - trade.price) * trade.quantity
                
                position.realized_pnl += realized_pnl - trade.commission
                position.side = PositionSide.FLAT
                position.quantity = 0
            else:
                # Update position
                if (position.quantity > 0) == (new_quantity > 0):  # Same direction
                    # Calculate weighted average entry price
                    total_cost = (abs(position.quantity) * position.entry_price + 
                                trade.quantity * trade.price)
                    position.entry_price = total_cost / abs(new_quantity)
                else:  # Direction change
                    position.entry_price = trade.price
                    position.entry_time = trade.timestamp
                
                position.quantity = new_quantity
                position.side = PositionSide.LONG if new_quantity > 0 else PositionSide.SHORT
            
            position.commission += trade.commission
            position.current_price = current_price
            position.last_update = trade.timestamp
        
        self.logger.debug(f"Updated position for {symbol}: {self.positions[symbol].quantity}")
    
    def update_prices(self, market_data: MarketData) -> None:
        """Update position prices.
        
        Args:
            market_data: Current market data
        """
        if market_data.symbol in self.positions:
            position = self.positions[market_data.symbol]
            position.current_price = market_data.close
            position.last_update = market_data.timestamp
            
            # Calculate unrealized P&L
            if not position.is_flat:
                if position.is_long:
                    position.unrealized_pnl = (market_data.close - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - market_data.close) * abs(position.quantity)
    
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value.
        
        Returns:
            Total portfolio value
        """
        total_value = self.cash
        
        for position in self.positions.values():
            if not position.is_flat:
                total_value += position.market_value
                total_value += position.unrealized_pnl
        
        return total_value
    
    def get_position_count(self) -> int:
        """Get number of open positions.
        
        Returns:
            Number of open positions
        """
        return sum(1 for p in self.positions.values() if not p.is_flat)
    
    def can_open_position(self) -> bool:
        """Check if new position can be opened.
        
        Returns:
            True if new position can be opened
        """
        return self.get_position_count() < self.max_positions
    
    def add_equity_point(self, timestamp: datetime) -> None:
        """Add equity curve point.
        
        Args:
            timestamp: Current timestamp
        """
        portfolio_value = self.get_portfolio_value()
        self.equity_curve.append((timestamp, portfolio_value))


class BacktestEngine:
    """Main backtesting engine."""
    
    def __init__(self, config: BacktestConfig):
        """Initialize backtest engine.
        
        Args:
            config: Backtesting configuration
        """
        self.config = config
        self.state = EngineState.IDLE
        
        # Components
        self.order_manager = OrderManager(config.commission_rate, config.slippage)
        self.portfolio_manager = PortfolioManager(config.initial_cash, config.max_positions)
        
        # Data management
        self.storage = get_storage()
        self.cache = get_cache()
        
        # Strategy management
        self.strategies: Dict[str, BaseStrategy] = {}
        
        # Event system
        self.event_queue = queue.Queue()
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
        
        # Performance tracking
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.processed_bars = 0
        self.total_bars = 0
        
        # Threading
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        self._lock = threading.Lock()
        
        # Logging
        self.logger = get_logger("backtest_engine")
        
        self.logger.info(f"Initialized backtest engine for {config.start_date} to {config.end_date}")
    
    def add_strategy(self, strategy: BaseStrategy) -> None:
        """Add strategy to engine.
        
        Args:
            strategy: Strategy to add
        """
        self.strategies[strategy.strategy_id] = strategy
        self.logger.info(f"Added strategy: {strategy.strategy_id}")
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """Remove strategy from engine.
        
        Args:
            strategy_id: Strategy ID to remove
            
        Returns:
            True if strategy was removed
        """
        if strategy_id in self.strategies:
            del self.strategies[strategy_id]
            self.logger.info(f"Removed strategy: {strategy_id}")
            return True
        return False
    
    def run(self, symbols: List[str]) -> Dict[str, BacktestResult]:
        """Run backtest for specified symbols.
        
        Args:
            symbols: List of symbols to backtest
            
        Returns:
            Dictionary of strategy results
        """
        if not self.strategies:
            raise ValueError("No strategies added")
        
        self.state = EngineState.RUNNING
        self.start_time = datetime.now()
        self._stop_event.clear()
        
        try:
            with log_performance(self.logger, "Backtest execution"):
                # Load and prepare data
                data_iterator = self._prepare_data(symbols)
                
                # Initialize strategies
                for strategy in self.strategies.values():
                    strategy.on_start()
                
                # Main execution loop
                self._execute_backtest(data_iterator)
                
                # Finalize strategies
                for strategy in self.strategies.values():
                    strategy.on_stop()
        
        except Exception as e:
            self.logger.error(f"Backtest failed: {e}")
            self.state = EngineState.ERROR
            raise
        
        finally:
            self.end_time = datetime.now()
            self.state = EngineState.STOPPED
        
        # Generate results
        return self._generate_results()
    
    def _prepare_data(self, symbols: List[str]) -> Any:
        """Prepare data for backtesting.
        
        Args:
            symbols: List of symbols
            
        Returns:
            Data iterator
        """
        self.logger.info(f"Preparing data for {len(symbols)} symbols")
        
        # Load data for each symbol
        symbol_data = {}
        
        for symbol in symbols:
            data = self.storage.load_data(
                symbol=symbol,
                timeframe=self.config.data_frequency,
                start_date=self.config.start_date,
                end_date=self.config.end_date
            )
            
            if data is None or data.empty:
                self.logger.warning(f"No data found for {symbol}")
                continue
            
            symbol_data[symbol] = data
            self.logger.info(f"Loaded {len(data)} bars for {symbol}")
        
        if not symbol_data:
            raise ValueError("No data loaded for any symbol")
        
        # Create unified timeline
        return self._create_data_iterator(symbol_data)
    
    def _create_data_iterator(self, symbol_data: Dict[str, pd.DataFrame]):
        """Create unified data iterator.
        
        Args:
            symbol_data: Dictionary of symbol DataFrames
            
        Yields:
            Market data events
        """
        # Get all unique timestamps
        all_timestamps = set()
        for data in symbol_data.values():
            all_timestamps.update(data.index)
        
        timestamps = sorted(all_timestamps)
        self.total_bars = len(timestamps)
        
        self.logger.info(f"Created timeline with {len(timestamps)} timestamps")
        
        # Yield data for each timestamp
        for timestamp in timestamps:
            if self._stop_event.is_set():
                break
            
            # Wait if paused
            while self._pause_event.is_set() and not self._stop_event.is_set():
                time.sleep(0.1)
            
            # Yield market data for each symbol at this timestamp
            for symbol, data in symbol_data.items():
                if timestamp in data.index:
                    row = data.loc[timestamp]
                    
                    market_data = MarketData(
                        symbol=symbol,
                        timestamp=timestamp,
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=row['volume']
                    )
                    
                    yield market_data
    
    def _execute_backtest(self, data_iterator) -> None:
        """Execute main backtest loop.
        
        Args:
            data_iterator: Data iterator
        """
        self.logger.info("Starting backtest execution")
        
        for market_data in data_iterator:
            if self._stop_event.is_set():
                break
            
            try:
                # Update portfolio with current prices
                self.portfolio_manager.update_prices(market_data)
                
                # Process pending orders
                fills = self.order_manager.process_orders(market_data)
                
                # Execute fills
                for order, trade in fills:
                    self.portfolio_manager.update_position(trade, market_data.close)
                    
                    # Notify strategy
                    strategy = self.strategies.get(order.strategy_id)
                    if strategy:
                        strategy.execute_trade(order, trade.price, trade.quantity)
                
                # Update strategies with market data
                for strategy in self.strategies.values():
                    if market_data.symbol in strategy.symbols:
                        strategy.update_data(market_data)
                        
                        # Process new orders from strategy
                        for order in strategy.orders.values():
                            if order.status == OrderStatus.PENDING and order.id not in self.order_manager.pending_orders:
                                self.order_manager.submit_order(order)
                
                # Update equity curve
                self.portfolio_manager.add_equity_point(market_data.timestamp)
                
                self.processed_bars += 1
                
                # Progress logging
                if self.processed_bars % 1000 == 0:
                    progress = (self.processed_bars / self.total_bars) * 100
                    self.logger.info(f"Progress: {progress:.1f}% ({self.processed_bars}/{self.total_bars})")
            
            except Exception as e:
                self.logger.error(f"Error processing bar {self.processed_bars}: {e}")
                if self.config.stop_on_error:
                    raise
        
        self.logger.info(f"Backtest completed: {self.processed_bars} bars processed")
    
    def _generate_results(self) -> Dict[str, BacktestResult]:
        """Generate backtest results.
        
        Returns:
            Dictionary of strategy results
        """
        results = {}
        
        for strategy_id, strategy in self.strategies.items():
            # Calculate performance metrics
            metrics = strategy.get_performance_metrics()
            
            # Calculate additional metrics
            duration = self.end_time - self.start_time if self.end_time and self.start_time else timedelta(0)
            bars_per_second = self.processed_bars / duration.total_seconds() if duration.total_seconds() > 0 else 0
            
            # Get equity curve
            equity_curve = self.portfolio_manager.equity_curve.copy()
            
            # Calculate returns
            initial_value = self.config.initial_cash
            final_value = self.portfolio_manager.get_portfolio_value()
            total_return = (final_value - initial_value) / initial_value if initial_value > 0 else 0
            
            # Calculate annualized return
            days = (self.config.end_date - self.config.start_date).days
            years = days / 365.25 if days > 0 else 1
            annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
            
            # Create result
            result = BacktestResult(
                strategy_id=strategy_id,
                config=self.config,
                start_time=self.start_time or datetime.now(),
                end_time=self.end_time or datetime.now(),
                duration=duration,
                total_bars=self.total_bars,
                processed_bars=self.processed_bars,
                
                # Performance metrics
                initial_portfolio_value=initial_value,
                final_portfolio_value=final_value,
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=metrics.get('volatility', 0),
                sharpe_ratio=metrics.get('sharpe_ratio', 0),
                max_drawdown=metrics.get('max_drawdown', 0),
                
                # Trading metrics
                total_trades=metrics.get('total_trades', 0),
                winning_trades=metrics.get('winning_trades', 0),
                losing_trades=metrics.get('losing_trades', 0),
                win_rate=metrics.get('win_rate', 0),
                avg_win=0,  # TODO: Calculate
                avg_loss=0,  # TODO: Calculate
                profit_factor=0,  # TODO: Calculate
                
                # Portfolio metrics
                final_cash=self.portfolio_manager.cash,
                final_positions=self.portfolio_manager.positions.copy(),
                max_positions_held=max(len([p for p in self.portfolio_manager.positions.values() if not p.is_flat]), 0),
                
                # Execution metrics
                bars_per_second=bars_per_second,
                memory_usage_mb=0,  # TODO: Calculate
                
                # Additional data
                equity_curve=equity_curve,
                trades=strategy.trades.copy(),
                orders=list(strategy.orders.values()),
                custom_metrics=metrics
            )
            
            results[strategy_id] = result
            
            self.logger.info(
                f"Strategy {strategy_id} results: "
                f"Return: {total_return:.2%}, "
                f"Sharpe: {metrics.get('sharpe_ratio', 0):.2f}, "
                f"Max DD: {metrics.get('max_drawdown', 0):.2%}, "
                f"Trades: {metrics.get('total_trades', 0)}"
            )
        
        return results
    
    def pause(self) -> None:
        """Pause backtest execution."""
        self._pause_event.set()
        self.state = EngineState.PAUSED
        self.logger.info("Backtest paused")
    
    def resume(self) -> None:
        """Resume backtest execution."""
        self._pause_event.clear()
        self.state = EngineState.RUNNING
        self.logger.info("Backtest resumed")
    
    def stop(self) -> None:
        """Stop backtest execution."""
        self._stop_event.set()
        self._pause_event.clear()
        self.state = EngineState.STOPPED
        self.logger.info("Backtest stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current engine status.
        
        Returns:
            Status dictionary
        """
        progress = (self.processed_bars / self.total_bars) * 100 if self.total_bars > 0 else 0
        
        return {
            'state': self.state.value,
            'progress': progress,
            'processed_bars': self.processed_bars,
            'total_bars': self.total_bars,
            'strategies': len(self.strategies),
            'pending_orders': len(self.order_manager.pending_orders),
            'portfolio_value': self.portfolio_manager.get_portfolio_value(),
            'cash': self.portfolio_manager.cash,
            'positions': self.portfolio_manager.get_position_count()
        }
    
    def __str__(self) -> str:
        """String representation."""
        return f"BacktestEngine(state={self.state.value}, strategies={len(self.strategies)})"