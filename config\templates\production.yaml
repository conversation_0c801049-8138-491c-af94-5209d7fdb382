# Production Configuration for Crypto Backtesting System
# This configuration is optimized for production deployment

# Application settings
app:
  name: "crypto-backtester"
  version: "0.1.0"
  environment: "production"
  debug: false
  timezone: "UTC"

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/production.log"
  max_size: "500MB"
  backup_count: 10
  console: false
  colored: false
  
  # Module-specific logging levels
  loggers:
    backtrader: "WARNING"
    ccxt: "ERROR"
    urllib3: "ERROR"
    requests: "ERROR"

# Hardware optimization (Ryzen 9 7950X + 96GB DDR5) - Full utilization
hardware:
  cpu:
    cores: 16
    threads: 32
    workers: 30  # Aggressive for production
    affinity: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]  # All cores
    
  memory:
    total_gb: 96
    available_gb: 88  # Reserve 8GB for OS
    chunk_size_mb: 1024  # Larger chunks for efficiency
    cache_size_gb: 16  # Larger cache
    gc_threshold: 0.85
    
  gpu:
    enabled: true
    device: "cuda:0"  # RTX 5060Ti
    memory_gb: 16
    compute_capability: "8.9"
    batch_size: 1024  # Larger batches

# Parallel processing - Maximum performance
parallel:
  enabled: true
  max_workers: 30
  chunk_size: 5000  # Larger chunks
  timeout: 7200  # 2 hours
  memory_limit_gb: 2.5  # Per worker
  
  # Process pool settings
  pool:
    type: "process"
    initializer: null
    maxtasksperchild: 1000  # More tasks per child
    
  # Task scheduling
  scheduler:
    strategy: "load_balanced"
    queue_size: 10000
    batch_size: 50

# Data management
data:
  # Storage paths
  paths:
    raw: "/data/crypto-backtester/raw"
    processed: "/data/crypto-backtester/processed"
    cache: "/data/crypto-backtester/cache"
    results: "/data/crypto-backtester/results"
    
  # Data sources
  exchanges:
    binance:
      enabled: true
      api_key: "${BINANCE_API_KEY}"  # Environment variable
      api_secret: "${BINANCE_API_SECRET}"
      sandbox: false  # Live data
      rate_limit: 1200
      
    coinbase:
      enabled: true
      api_key: "${COINBASE_API_KEY}"
      api_secret: "${COINBASE_API_SECRET}"
      sandbox: false
      
    kraken:
      enabled: true
      api_key: "${KRAKEN_API_KEY}"
      api_secret: "${KRAKEN_API_SECRET}"
      
  # Data quality - Stricter in production
  quality:
    min_data_points: 500
    max_missing_ratio: 0.02
    outlier_threshold: 2.5
    validate_ohlcv: true
    
  # Caching - Optimized for performance
  cache:
    enabled: true
    backend: "redis"  # Faster than HDF5
    ttl: 3600  # 1 hour
    compression: "lz4"  # Faster compression
    
# Backtesting engine
engine:
  # Default settings
  initial_cash: 1000000  # $1M for production
  commission: 0.001
  slippage: 0.0001
  
  # Risk management - Conservative
  risk:
    max_position_size: 0.05  # 5% of portfolio
    max_drawdown: 0.15  # 15%
    stop_loss: 0.03  # 3%
    take_profit: 0.10  # 10%
    
  # Performance tracking
  metrics:
    calculate_all: true
    benchmark: "BTC/USDT"
    risk_free_rate: 0.02
    
# Strategy configuration
strategies:
  # Default parameters
  defaults:
    timeframe: "1h"
    lookback: 200  # Longer lookback
    min_periods: 50
    
  # Strategy registry - Production strategies only
  registry:
    ma_crossover:
      class: "MovingAverageCrossover"
      params:
        fast_period: 12
        slow_period: 26
        
    rsi_divergence:
      class: "RSIDivergenceStrategy"
      params:
        period: 14
        oversold: 25
        overbought: 75
        
    bollinger_mean_reversion:
      class: "BollingerMeanReversionStrategy"
      params:
        period: 20
        std_dev: 2.5
        
    macd_momentum:
      class: "MACDMomentumStrategy"
      params:
        fast_period: 12
        slow_period: 26
        signal_period: 9
        
# Optimization settings - Production grade
optimization:
  # Algorithm settings
  algorithms:
    grid_search:
      enabled: true
      max_combinations: 100000  # Larger search space
      
    bayesian:
      enabled: true
      n_calls: 500  # More iterations
      acq_func: "EI"
      
    genetic:
      enabled: true
      population_size: 200  # Larger population
      generations: 500
      mutation_rate: 0.05  # Lower mutation rate
      
  # Objectives
  objectives:
    primary: "calmar_ratio"  # Risk-adjusted returns
    secondary: "sharpe_ratio"
    constraints:
      min_trades: 50
      max_drawdown: 0.15
      
  # Performance thresholds - Higher standards
  thresholds:
    min_sharpe: 1.5
    min_win_rate: 0.45
    max_drawdown: 0.15
    min_calmar: 1.0
    
# Monitoring and alerting - Comprehensive
monitoring:
  enabled: true
  interval: 10  # seconds - More frequent
  
  # System metrics
  metrics:
    cpu: true
    memory: true
    disk: true
    gpu: true
    network: true
    
  # Alerts - Tighter thresholds
  alerts:
    cpu_threshold: 0.85
    memory_threshold: 0.85
    disk_threshold: 0.85
    gpu_threshold: 0.90
    
  # Dashboard
  dashboard:
    enabled: true
    host: "0.0.0.0"  # Accept external connections
    port: 8080
    auto_refresh: 2  # seconds
    ssl: true
    
# Database configuration - Production grade
database:
  # Results storage
  results:
    type: "postgresql"
    host: "${DB_HOST}"
    port: 5432
    database: "crypto_backtester"
    username: "${DB_USERNAME}"
    password: "${DB_PASSWORD}"
    pool_size: 20
    
  # Cache storage
  cache:
    type: "redis"
    host: "${REDIS_HOST}"
    port: 6379
    db: 0
    password: "${REDIS_PASSWORD}"
    
# Security settings - Enhanced
security:
  # API keys (use environment variables)
  env_vars:
    - "BINANCE_API_KEY"
    - "BINANCE_API_SECRET"
    - "COINBASE_API_KEY"
    - "COINBASE_API_SECRET"
    - "KRAKEN_API_KEY"
    - "KRAKEN_API_SECRET"
    - "DB_HOST"
    - "DB_USERNAME"
    - "DB_PASSWORD"
    - "REDIS_HOST"
    - "REDIS_PASSWORD"
    
  # Encryption
  encryption:
    enabled: true
    algorithm: "AES-256"
    key: "${ENCRYPTION_KEY}"
    
  # Rate limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 1000
    
# Production optimizations
production:
  # Performance
  performance:
    precompile_numba: true
    optimize_imports: true
    lazy_loading: true
    
  # Memory management
  memory:
    aggressive_gc: true
    memory_mapping: true
    swap_usage: false
    
  # Error handling
  error_handling:
    retry_attempts: 3
    retry_delay: 5
    circuit_breaker: true
    
# Feature flags - Production ready features
features:
  gpu_acceleration: true
  parallel_optimization: true
  real_time_monitoring: true
  advanced_metrics: true
  machine_learning: true
  auto_scaling: true
  
# External services
services:
  # Data providers
  data_providers:
    alpha_vantage:
      enabled: true
      api_key: "${ALPHA_VANTAGE_API_KEY}"
      
    quandl:
      enabled: true
      api_key: "${QUANDL_API_KEY}"
      
  # Notification services
  notifications:
    email:
      enabled: true
      smtp_server: "${SMTP_SERVER}"
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      
    discord:
      enabled: true
      webhook_url: "${DISCORD_WEBHOOK_URL}"
      
# Backup and recovery
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  destinations:
    - "s3://crypto-backtester-backups/"
    - "/backup/crypto-backtester/"
    
# Scaling configuration
scaling:
  auto_scaling:
    enabled: true
    min_workers: 16
    max_workers: 64
    cpu_threshold: 0.8
    memory_threshold: 0.8
    
  load_balancing:
    enabled: true
    algorithm: "least_connections"
    health_check_interval: 30
    
# Compliance and auditing
compliance:
  auditing:
    enabled: true
    log_all_trades: true
    log_parameter_changes: true
    retention_years: 7
    
  reporting:
    daily_reports: true
    weekly_summaries: true
    monthly_analysis: true